<script setup lang="ts" name="typicalscenario">
import {onActivated, ref} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import {
	ledgerDataAnalysis,
	ledgerDataAnalysisBatchDelete,
	getledgerDataAnalysis,
	getLedgerStatistics,
	deleteLedgerStatisticsById,
	downloadLedgerStatistics,
} from '@/api/LedgerApi'
import {ElMessage, ElMessageBox} from 'element-plus'
import {useUserStore} from '@/stores/useUserStore'
const route = useRoute()
const router = useRouter()
import {USER_ROLES_ENUM} from '@/define/organization.define'
const currentUser = ref(JSON.parse(localStorage.getItem('currentUserInfo') as string))
// 数据管理岗位权限
const role = ref(currentUser.value.staffRole.some((item) => item === USER_ROLES_ENUM.DATA_LEADER))
const chartType = ref([
	{name: '统计表', id: 0},
	{name: '柱状图', id: 1},
	{name: '桑葚图', id: 2},
	{name: '环形图', id: 3},
	{name: '饼图', id: 4},
	{name: '折线图', id: 5},
])
// 表格中的操作列
const buttons: any = [
	{code: 'view', label: '查看', icon: 'i-majesticons-eye-line', verify: 'true'},
	{
		code: 'edit',
		label: '编辑',
		type: 'primary',
		icon: 'i-majesticons-pencil-alt-line',
		verify: 'true',
		disabled: `${!role.value}`,
	},
	// {
	// 	code: 'download',
	// 	label: '下载',
	// 	type: 'info',
	// 	icon: 'i-ic-baseline-download',
	// 	verify: 'true',
	// },
	{
		code: 'delete',
		label: '删除',
		type: 'danger',
		icon: 'i-ic-round-dangerous',
		verify: 'true',
		disabled: `${!role.value}`,
	},
]

// 查询条件
const runwayForm: any = ref({
	name: '',
})
// 设置表格高度计算
const tableHeight = ref(0)
const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 80
}
// 多选数据
const statisticsList: any = ref([])
const selectedCount = ref(0)

const addAnalysis = (e: any) => {
	router.push({path: '/ledger/addAndEditAnalysis'})
	// if (e === 'a') {
	// 	router.push({path: '/ledger/addAndEditAnalysis'})
	// }
}
const dataList = ref([
	{name: '市司法局通过一表通在全国率先实现安置帮教对象服务管理模式升级'},
	{name: '永川区、铜梁区、奉节县试点一表通V2.0'},
	{name: '城口县试点全域村（社区）推广应用一表通'},
	{name: '万州区医保局“一表通”精准服务困难人群参保'},
	{name: '黔江区打造应急物资精准配置应用场景'},
	{name: '黔江区打造市场主体培育一表统揽应用场景'},
	{name: '涪陵区使用“一表通”精准识别优抚对象'},
	{name: '渝中区两路口街道运用一表通筑牢安全防护网'},
	{name: '渝中区“一表通”数据赋能助力民生政策精准直达'},
	{name: '大渡口区推动安全风险管控一表统揽、一屏掌控、一键调度'},
	{name: '大渡口区“一表通”助力基层提升残疾人工作质效'},
	{name: '江北区“一表通”打破数据孤岛促进就业'},
	{name: '江北区数据赋能扶残助残'},
	{name: '九龙坡区运用“一表通”实现助残政策快速审批精准落实'},
	{name: '南岸区使用“一表通”织密安全排查网'},
	{name: '渝北区委办公室打造弱势群体走访慰问典型场景'},
	{name: '北碚区歇马街道依托“一表通”织密“森林防火网”典型场景'},
	{name: '北碚区天生街道一表通助力城市管理精准发力'},
	{name: '巴南区“一表通”破解安置帮教工作盲点、难点、堵点'},
	{name: '巴南区一表通赋能独居老人安全守护'},
	{name: '长寿区数字赋能动火作业全过程监管'},
	{name: '江津区吴滩镇整合一表通数据保障重点对象住房安全'},
	{name: '永川区重精患者“一表通管”'},
	{name: '南川区水江镇打造数据采集一表通惠民政策精准享典型场景'},
	{name: '南川区依托一表通数据形成特色子跑道、特色体征指标'},
	{name: '綦江区打造困难大学生教育救助事项一表通办典型场景'},
	{name: '大足区农村黑臭水体排查整治典型场景'},
	{name: '大足区龙滩子街道探索运用“一表通”赋能高龄老人关怀与健康监测'},
	{name: '璧山区以“一表通”助力消防生命通道整治'},
	{name: '铜梁区数据“一表通”构建森林“防火墙”典型场景'},
	{name: '潼南区双江镇聚焦惠民暖民以数字赋能助推基层治理质效提升'},
	{name: '潼南区一表通助力抗旱救灾'},
	{name: '荣昌区“一表通”助力院落微治理'},
	{name: '荣昌区探索使用“一表通”规范中央资金项目落实情况'},
	{name: '开州区推广应用“一表通”减负增效典型场景'},
	{name: '武隆区江口镇以“一表通”为引擎，重塑精神障碍患者服务生态'},
	{name: '武隆区数字赋能欠薪隐患排查'},
	{name: '城口县打造数字赋能信访服务智慧助力残障人士典型场景'},
	{name: '丰都县依托“一表通”实现高龄津贴发放“零报表”'},
	{name: '垫江县坪山镇通过“一表通”赋能基层“智”理'},
	{name: '忠县推动“一表通”落地落实为基层减负、为救灾赋能'},
	{name: '忠县跨部门维护一张表为民营经营主体减负纾困解难'},
	{name: '"奉节县农村环境治理典型场景	"'},
	{name: '云阳县以“一表通”赋能森林火灾救援'},
	{name: '巫山县铜鼓镇信息互联畅通应急渠道'},
	{name: '巫溪县启航老年服务新纪元'},
	{name: '巫溪县宁厂镇打造残疾人服务新场景'},
	{name: '石柱县中益乡综合报表“一表统”：助力基层减负赋能'},
	{name: '石柱县南宾街道灾情信息一表统揽为基层减负'},
	{name: '彭水县一表通赋能巩固脱贫攻坚成果与乡村振兴有效衔接'},
	{name: '两江新区“一表通”助力“居民兴趣活动”服务精准匹配'},
	{name: '两江新区“一表通”助力应急安全工作开展'},
	{name: '高新区推动“两违”整治“一表整改”'},
	{name: '高新区赋能专业市场“一表监管”'},
	{name: '万盛经开区“一表通”助力关爱未成年人工作减负增效'},
])

// 表中的内容
const tableData: any = ref([])
// 表头
const colData: any = ref([
	{
		prop: 'name',
		label: '市级典型场景',
	},
])
const active = ref(0)
const url = ref('')

//表格与分页关联
const reqParams = ref({
	skipCount: 0,
	maxResultCount: 10,
})
// 分页相关参数
const pagination = ref({
	total: 0,
	page: 1,
	size: 10,
})
// 查询
const getList = () => {
	if (runwayForm.value.name) {
		let data: any = []
		dataList.value.forEach((item: any) => {})
		const searchData = dataList.value.filter((item: any) => {
			return fuzzyMatch(item.name, runwayForm.value.name)
		})
		tableData.value = JSON.parse(JSON.stringify(searchData)).splice(
			(pagination.value.page - 1) * pagination.value.size,
			pagination.value.size
		)
		pagination.value.total = searchData.length
	} else {
		tableData.value = JSON.parse(JSON.stringify(dataList.value)).splice(
			(pagination.value.page - 1) * pagination.value.size,
			pagination.value.size
		)
		pagination.value.total = dataList.value.length
	}
	runwayForm.value.name
}

const fuzzyMatch = (text: any, pattern: any) => {
	// 将模糊词转换为正则表达式
	const regex = new RegExp(pattern.split('').join('.*'), 'i')
	return regex.test(text)
}
// 高级查询
const seniorList = () => {
	pagination.value.page = 1
	pagination.value.size = 10
	getList()
}
// 清空
const empty = () => {
	runwayForm.value.name = ''
	runwayForm.value.chartType = null
	getList()
}
// 分页事件
const onPaginationChange = (val: any, type: any) => {
	if (type === 'page') {
		pagination.value.page = val
		reqParams.value.skipCount = (val - 1) * pagination.value.size
	} else {
		pagination.value.page = 1
		pagination.value.size = val
		reqParams.value.maxResultCount = pagination.value.size
	}
	getList()
}

onActivated(() => {
	getList()
})
</script>
<template>
	<div class="dataAnalysis">
		<Block
			:enable-fixed-height="true"
			:enable-expand-content="true"
			:enableBackButton="false"
			:title="'典型场景'"
			@heightChanged="onBlockHeightChanged"
		>
			<template #expand>
				<div class="search-box" v-action:enter="seniorList">
					<el-input
						placeholder="请输入典型场景"
						v-model="runwayForm.name"
						style="width: 250px; margin-right: 10px"
					></el-input>
					<el-button size="default" type="primary" @click="seniorList">
						<i class="i-ic-baseline-send" style="margin-right: 3px"></i>
						查询</el-button
					>
					<el-button size="default" type="default" @click="empty">
						<i class="i-ic-baseline-cleaning-services" style="margin-right: 3px"></i>
						重置
					</el-button>
				</div>
			</template>
			<TableV2
				ref="tableRef"
				:height="tableHeight"
				:columns="colData"
				:defaultTableData="tableData"
				:headers="{Urlkey: 'ledger'}"
				:enableToolbar="false"
				:enable-create="false"
				:enable-edit="false"
				:enable-delete="false"
				:enableSelection="false"
				:disabled="true"
				:enableIndex="true"
				:req-params="reqParams"
				:buttons="buttons"
			>
			</TableV2>
			<Pagination
				:total="pagination.total"
				:page-size="pagination.size"
				:current-page="pagination.page"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			>
			</Pagination>
		</Block>
	</div>
</template>
<route>
	{
		meta:{
			title:'典型场景'
		}
	}
</route>
<style scoped lang="scss">
.search-box {
	align-items: start;
	display: flex;
	padding: 10px 15px 10px 10px;
}
</style>
