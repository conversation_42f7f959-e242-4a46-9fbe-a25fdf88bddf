<script setup lang="ts" name="addandeditanalysis">
import {
  onMounted,
  reactive,
  ref,
  toRaw,
  nextTick,
  onBeforeUpdate,
  onActivated,
} from "vue";
import { ElMessage, FormRules, ElMessageBox } from "element-plus";
import { RunwayList } from "@/define/ledger.define";
import { useRouter, useRoute } from "vue-router";
import { useUserStore } from "@/stores/useUserStore";
import draggable from 'vuedraggable'
import {
  getDetailByLedgerId,
  myOnlineLedgers,

} from "@/api/LedgerApi";
import { useLedgerDN } from "@/hooks/useConvertHook";
import { Plus, Search, View } from "@element-plus/icons-vue";
import { da, fa } from "element-plus/es/locale";
import { dayjs } from "element-plus";
import { useValidateCode } from '@/hooks/useValidateCode'
import { updateLabelTitle } from "@/hooks/useLabels";
const cancelOpen = ref(false);
const codeTitle = ref("数据透视解除脱敏");
const loading = ref(false);
const queryId: any = ref("");
const route = useRoute();
const router = useRouter();
const user = useUserStore();
const cancelCode = ref({ val1: '', val2: '', val3: '', val4: '' })
const useCode = useValidateCode()
const ruleRef = ref();
const formRef = ref();
const showFreateAnalysis = ref(false);
const whichPart: any = ref(0); // 第几步
const changeTabValue = ref()
import showSmartCharts from './components/showSmartCharts.vue'
import { mockDataService, type TableRow, type ChartData } from './mockDataService'

const whichPartList = ref([
  {
    name: "提取数据",
    type: 1,
    show: true,
  },
  {
    name: "分析配置",
    type: 2,
    show: false,
  },
]);
// 设置表格高度计算
const tableHeight = ref(0);
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 130;
};


const formData: any = ref({})
const defaultData: any = ref({}) //表单回显
const formArray: any = ref([
  {
    type: "text",
    label: "分可视化名称",
    prop: "name",
  },
  {
    type: "textarea",
    label: "分析说明",
    prop: "analysisDescription",
    placeholder: "请输入分析说明",
  },
  {
    type: "select",
    label: "选择业务表",
    prop: "tableInfoId",
    placeholder: "请选择业务表",
    multiple: false,
    filterable: true,
    multipleLimit: 1,
    options: [],
  },
]);
const formRules = reactive<FormRules>({
  name: [{ required: true, message: "请输入透视名称", trigger: "blur" }],
  select: [{ required: true, message: "请选择需要透视的业务表", trigger: "blur" }],
});
// 通过id获取选中业务表的表头
const setLedgerData = async (ledgerId: any) => {
  const res = await getDetailByLedgerId(ledgerId);
  columnsList.value = res.data.tableInfo.fields;
};
// 设置第二步
const setWhichPart = () => {
  whichPartList.value.forEach((item) => {
    item.show = false;
    if (item.type == 2) item.show = true;
  });
  whichPart.value = 1;
};
const formChange = (val: any, field: any) => {

};
const onSave = () => {
  console.log(calculationPanel.filters);
  console.log(calculationPanel.dimensions);
  // 获取当前时间
const now = new Date();
 
// 使用模板字符串格式化日期和时间
const formattedDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
 
console.log(formattedDate); // 输出格式如 "2023-04-01 15:30:45"
  let data = {
    name:formData.value.name,
    analysisDescription:formData.value.analysisDescription,
    tableInfoId:formData.value.tableInfoId,
    canvasComponents:canvasComponents.value,
    controlPanelItemsData:controlPanelItemsData.value,
    filters:calculationPanel.filters,
    dimensions:calculationPanel.dimensions,
    creationTime:formattedDate,
    echart:'柱状图'
  }
  console.log(data);
  mockDataService.dataList.push(data)
  // formData
  router.go(-1);
};
const onBack = () => {
  if (!queryId.value) {
    if (whichPart.value == 0) {
      router.go(-1);
    } else if (whichPart.value == 1) {
      whichPartList.value.forEach((item) => {
        item.show = false;
        if (item.type == 1) item.show = true;
      });
      whichPart.value = 0;
      // 提取数据回显
    }
  } else {
    router.go(-1);
  }

  // router.go(-1)
};


// 提取数据按钮
const onFormSubmit = async (form: any) => {
  setWhichPart()
  setLedgerData(form.tableInfoId)
};


const statistRef = ref();

const tabList = ref([
  {
    name: "数据提取结果",
    isActive: true,
    index: 1,
  },
]);
const departmentForm: any = ref({
  currentPage: 1,
  MaxResultCount: 10,
  total: 0,
});

// 筛选表头
const columnsList: any = ref([
  {
    "displayName": "所属城市",
    "type": "string",
  },
  {
    "displayName": "数据来源",
    "type": "string",
  },
  {
    "displayName": "填报人",
    "type": "string",
  },
  {
    "displayName": "编辑人",
    "type": "string",
  },
  {
    "name": "District",
    "displayName": "所属区县",
  },
  {
    "displayName": "更新时间",
    "type": "datetime",
  },
  {
    "displayName": "所属街镇",
    "type": "string",
  },
  {
    "displayName": "所属村社",
    "type": "string",
  }
]);

//  业务表列表
const ledgerList = ref();
// 获取当前用户所有业务表
const getLedger = async (id: any) => {
  let data = {
    LedgerTypeId: id,
    SkipCount: 0,
    MaxResultCount: 10000,
  };
  const res = await myOnlineLedgers();
  res.data.forEach((item: any) => {
    item.label = item.name;
    item.value = item.id;
  });
  ledgerList.value = res.data;
  formArray.value.forEach((item: any) => {
    if (item.label == "选择业务表") item.options = res.data;
  });

};





onActivated(() => {
  getLedger("");
  queryId.value = route.query.id
  if(route.query.id){
    let data:any  = {}
    let dataList =  JSON.parse(JSON.stringify(mockDataService.dataList))
    dataList.forEach(item =>{
      if(item.tableInfoId == route.query.id)
      data = item
    })
    formData.value.name = data.name
    formData.value.analysisDescription = data.analysisDescription
    formData.value.tableInfoId = data.tableInfoId
    canvasComponents.value = data.canvasComponents
    controlPanelItemsData.value = data.controlPanelItemsData
    calculationPanel.filters = data.filters
    calculationPanel.filters = data.filters
    calculationPanel.dimensions = data.dimensions
  }
  whichPartList.value.forEach((item) => {
    item.show = false;
    if (item.type == 1) item.show = true;
  });
  whichPart.value = 0;
});

// 基础数据
// 左侧控件面板数据
const controlPanelItems = ref([
  {
    id: 'table',
    name: '明细表',
    icon: 'i-majesticons-table',
    type: 'table',
    description: '数据表格展示'
  },
  {
    id: 'bar-chart',
    name: '柱状图',
    icon: 'i-majesticons-chart-bar',
    type: 'chart',
    chartType: 'bar',
    description: '柱状图表'
  },
  {
    id: 'pie-chart',
    name: '饼图',
    icon: 'i-majesticons-chart-pie',
    type: 'chart',
    chartType: 'pie',
    description: '饼图表'
  },
  {
    id: 'line-chart',
    name: '折线图',
    icon: 'i-majesticons-chart-line',
    type: 'chart',
    chartType: 'line',
    description: '折线图表'
  }
])

// 数据字段分类
const dataFields = ref({
  dimensions: [
    { name: 'str.所属区县', field: 'district', type: 'string' },
    { name: 'str.所属街镇', field: 'street', type: 'string' },
    { name: 'str.所属村社', field: 'village', type: 'string' },
    { name: 'str.身份证号', field: 'idCard', type: 'string' },
    { name: 'str.姓名', field: 'name', type: 'string' },
    { name: 'str.政治面貌', field: 'politicalStatus', type: 'string' },
    { name: 'str.性别', field: 'gender', type: 'string' },
    { name: 'str.学历', field: 'education', type: 'string' },
    { name: 'str.职业', field: 'occupation', type: 'string' }
  ],
  measures: [
    { name: 'No.年龄', field: 'age', type: 'number' },
    { name: 'No.孕次', field: 'visits', type: 'number' },
    { name: 'No.收入', field: 'income', type: 'number' }
  ],
  time: [
    { name: 'tm.出生日期', field: 'birthDate', type: 'date' }
  ]
})

// 当前选中的数据表信息
const currentTableInfo = ref({
  name: '女性网格员生育情况表',
  totalRecords: 0,
  selectedFields: []
})

// 过滤器配置
const filterVisible = ref(false)
const filterForm = reactive({
  filterMethod: 'include', // include: 按条件过滤, exclude: 按条件过滤
  conditionType: 'single', // single: 单条件, or: 或条件, and: 且条件
  conditions: []
})

// 画布中的组件
const canvasComponents = ref([])




// 预览模态窗口状态
const previewVisible = ref(false)
const previewData = ref([])
const previewComponent = ref(null)
const previewTableData = ref([])
const previewPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 聚合运算配置弹窗
const aggregationVisible = ref(false)
const aggregationForm = reactive({
  measureColumn: '',
  operation: 'sum',
  resultName: '',
  groupColumn: ''
})

// 运算方式选项
const operationOptions = [
  { label: '求和', value: 'sum', description: '计算所有数值的总和' },
  { label: '平均值', value: 'average', description: '计算所有数值的平均值' },
  { label: '计数', value: 'count', description: '统计记录总数' },
  { label: '去重计数', value: 'distinctCount', description: '统计不重复值的数量' },
  { label: '最大值', value: 'max', description: '找出最大值' },
  { label: '最小值', value: 'min', description: '找出最小值' }
]

// 可用的度量列
const measureColumns = [
  { label: '年龄', value: 'age' },
  { label: '孕次', value: 'visits' },
  { label: '收入', value: 'income' }
]

// 可用的分组列
const groupColumns = [
  { label: '所属区县', value: 'district' },
  { label: '所属街镇', value: 'street' },
  { label: '性别', value: 'gender' },
  { label: '政治面貌', value: 'politicalStatus' },
  { label: '学历', value: 'education' },
  { label: '职业', value: 'occupation' }
]

// 运算结果
const aggregationResult = ref(null)
const aggregationResultVisible = ref(false)

// 右侧待计算栏数据
const calculationPanel = reactive({
  dimensions: [], // 维度字段
  measures: [], // 度量字段
  filters: [] // 过滤器字段
})

// 生成配置
const generateConfig = reactive({
  resultLimit: 100,
  showAll: false
})

// 拖拽结束处理
const onDragEnd = (evt) => {
  console.log('拖拽结束:', evt)
}

// 拖拽添加处理
const onDragAdd = (evt) => {
  // console.log('拖拽添加:', evt)
  // const { newIndex } = evt
  // if (canvasComponents.value[newIndex]) {
  // }
}


// 生成模拟数据
const generateMockData = (type: string, chartType: string | null = null) => {
  if (type === 'table') {
    return mockDataService.generateTableData(50)
  } else if (type === 'chart') {
    return mockDataService.generateChartData(chartType || 'bar')
  }
  return []
}

// 选择组件
const selectComponent = (component) => {
}

// 删除组件
const deleteComponent = (component) => {
  const index = canvasComponents.value.findIndex(c => c.id === component.id)
  if (index > -1) {
    canvasComponents.value.splice(index, 1)
  }
}

// 打开预览
const openPreview = (component) => {
  previewComponent.value = component
  previewData.value = component.data

  if (component.type === 'table') {
    previewPagination.total = component.data.length
    previewPagination.currentPage = 1
    updatePreviewTableData()
  }

  previewVisible.value = true
}

// 更新预览表格数据
const updatePreviewTableData = () => {
  const start = (previewPagination.currentPage - 1) * previewPagination.pageSize
  const end = start + previewPagination.pageSize
  previewTableData.value = previewData.value.slice(start, end)
}

// 预览分页变化
const handlePreviewPageChange = (page: number) => {
  previewPagination.currentPage = page
  updatePreviewTableData()
}

// 获取图表表格数据
const getChartTableData = () => {
  if (!previewComponent.value || previewComponent.value.type !== 'chart') return []

  const { categories, values } = previewComponent.value.data
  const total = values.reduce((sum, val) => sum + val, 0)

  return categories.map((category, index) => ({
    category,
    value: values[index],
    percentage: total > 0 ? `${((values[index] / total) * 100).toFixed(2)}%` : '0%'
  }))
}

// 导出数据
const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

// 保存设计
const saveDesign = () => {
  const designData = {
    components: canvasComponents.value,
    timestamp: new Date().toISOString(),
    version: '1.0'
  }

  localStorage.setItem('visualizationEngineV2_design', JSON.stringify(designData))
  ElMessage.success('设计已保存到本地')
}

// 加载设计
const loadDesign = () => {
  const savedDesign = localStorage.getItem('visualizationEngineV2_design')
  if (savedDesign) {
    try {
      const designData = JSON.parse(savedDesign)
      canvasComponents.value = designData.components || []
      ElMessage.success('设计已加载')
    } catch (error) {
      ElMessage.error('加载设计失败')
    }
  } else {
    ElMessage.info('没有找到保存的设计')
  }
}

// 复制组件
const duplicateComponent = (component) => {
  const newComponent = {
    ...component,
    id: `${component.id}_copy_${Date.now()}`,
    name: `${component.name} - 副本`,
    x: component.x + 20,
    y: component.y + 20
  }
  canvasComponents.value.push(newComponent)
  ElMessage.success('组件已复制')
}

// 打开过滤器设置
const openFilter = () => {
  filterVisible.value = true
}

// 确认过滤器设置
const confirmFilter = () => {
  ElMessage.success('过滤器设置已保存')
  filterVisible.value = false
}

// 添加过滤条件
const addFilterCondition = () => {
  filterForm.conditions.push({
    field: '',
    operator: 'equals',
    value: ''
  })
}

// 删除过滤条件
const removeFilterCondition = (index) => {
  filterForm.conditions.splice(index, 1)
}

// 更新表格信息
const updateTableInfo = () => {
  const sampleData = mockDataService.generateTableData(100)
  currentTableInfo.value.totalRecords = sampleData.length
}

// 字段添加到计算面板
const onFieldAdd = (evt) => {
  console.log('字段添加到计算面板:', evt)
}

// 字段变化处理（排序、添加、删除）
const onFieldChange = (evt) => {
  console.log('字段变化:', evt)
  if (evt.added) {
    ElMessage.success('字段已添加')
  } else if (evt.moved) {
    ElMessage.success('字段顺序已调整')
  }
}

// 克隆字段用于拖拽
const cloneField = (field) => {
  return {
    ...field,
    id: `${field.field}_${Date.now()}`
  }
}

// 点击添加字段到计算面板
const addFieldToCalculation = (field, targetType = 'dimensions') => {
  const clonedField = cloneField(field)

  // 检查是否已存在
  const exists = calculationPanel[targetType].some(item => item.field === field.field)
  if (exists) {
    ElMessage.warning('该字段已存在')
    return
  }

  // 根据字段类型自动选择目标区域
  if (field.type === 'number' && targetType === 'dimensions') {
    targetType = 'measures'
  }

  calculationPanel[targetType].push(clonedField)
  ElMessage.success(`字段已添加到${targetType === 'dimensions' ? '维度' : targetType === 'measures' ? '度量' : '过滤器'}`)
}

// 从计算面板移除字段
const removeFromCalculation = (type, index) => {
  calculationPanel[type].splice(index, 1)
  ElMessage.success('字段已移除')
}

// 生成分析
const generateAnalysis = () => {
  if (calculationPanel.dimensions.length === 0 && calculationPanel.measures.length === 0) {
    ElMessage.warning('请至少添加一个维度或度量字段')
    return
  }
  if(!controlPanelItemsData.value){
    ElMessage.warning('请选择生成的图表')
    return
  }
  
  let analysisComponent =null
// controlPanelItems
  // 根据配置生成不同类型的分析结果
  if (calculationPanel.filters.length > 0 && calculationPanel.dimensions.length > 0  && controlPanelItemsData.value == "bar-chart") {
    canvasComponents.value.push({
     echartType:1,
      chartCoordinateYList:[7,8,5,1],
      chartCoordinateXList:["硕士", "博士","本科","大专"]
  })
  } 
 

  ElMessage.success('分析结果已生成')
}




// 打开聚合运算配置
const openAggregation = () => {
  aggregationVisible.value = true
}

// 确认聚合运算配置
const confirmAggregation = () => {

  aggregationVisible.value = false
  // aggregationResultVisible.value = true

  ElMessage.success('聚合运算完成')
}

const controlPanelItemsData = ref('')
// 关闭运算结果
const closeAggregationResult = () => {
  aggregationResultVisible.value = false
  aggregationResult.value = null
}

// 将运算结果添加到画布
const addResultToCanvas = () => {
  if (!aggregationResult.value) return

  const newComponent = {
    id: `result_${Date.now()}`,
    name: aggregationResult.value.title,
    type: aggregationResult.value.type === 'group' ? 'chart' : 'table',
    chartType: 'bar',
    x: Math.random() * 200 + 50,
    y: Math.random() * 200 + 50,
    width: 400,
    height: 300,
    data: aggregationResult.value.type === 'group'
      ? {
        categories: aggregationResult.value.data.map(item => item.category),
        values: aggregationResult.value.data.map(item => item.value),
        chartType: 'bar'
      }
      : [{ result: aggregationResult.value.value }]
  }

  // canvasComponents.value.push(newComponent)
  closeAggregationResult()

  ElMessage.success('运算结果已添加到画布')
}

// 清空画布
const clearCanvas = () => {
  ElMessageBox.confirm('确定要清空画布吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    canvasComponents.value = []
    ElMessage.success('画布已清空')
  }).catch(() => { })
}
onMounted(async () => {
 });
</script>
<template>
  <div class="addAndEditAnalysis">
    <Block ref="statistRef" :enable-expand-content="false" :enableBackButton="false" :enable-fixed-height="true"
      @heightChanged="onBlockHeightChanged">
      <template #title>
        <div class="titleHeader">
          <p class="titleHeader-text">{{ queryId ? "编辑可视化" : "新建可视化" }}</p>
          <!-- <el-tooltip content="操作提示" effect="dark" class="box-item" placement="bottom">
						<el-button size="small" text type="primary">
							<el-icon :size="16" color=" rgb(23, 100, 206)">
								<Warning />
							</el-icon>
							操作提示</el-button>
					</el-tooltip> -->
        </div>
      </template>
      <template #topCenter>
        <div class="headerConter">
          <div v-for="(item, index) in whichPartList" :key="index"
            :class="[item.show ? 'step' : '', 'headerConter-number']">
            <span>{{ item.type }}</span>
            {{ item.name }}
          </div>
        </div>
      </template>
      <template #topRight>
        <el-button v-if="whichPart === 1" type="primary" size="small" style="margin-left: 10px"
          @click="onSave">保存</el-button>
        <el-button size="small" @click="onBack">返回</el-button>
      </template>
      <div class="statistics-detail" style="margin-top: 15px" v-if="whichPart === 0">
        <div class="statistics-detail-body">
          <Form ref="formRef" class="form" v-model="formData" :defaultData="defaultData" :rules="formRules"
            :showButton="false" :data="formArray" label-position="top" :enableReset="false" :enableClear="false"
            :confirmText="'提取数据'" @change="formChange" @submit="onFormSubmit">
          </Form>
        </div>
      </div>
      <div class="statistics-table" v-else>
        <div class="engine-container">
          <!-- 左侧控件面板 -->
          <div class="left-panel">
            <!-- 党的建设 -->
            <div class="panel-section">
              <!-- 维度 -->
              <div class="field-group">
                <div class="field-group-header">
                  <i class="i-majesticons-folder"></i>
                  <span>维度</span>
                  <i class="i-majesticons-help"></i>
                </div>
                <div class="field-list">
                  <draggable v-model="dataFields.dimensions" :group="{ name: 'fields', pull: 'clone', put: false }"
                    :sort="false" :clone="cloneField" item-key="field">
                    <template #item="{ element }">
                      <div class="field-item" @click="addFieldToCalculation(element, 'dimensions')">
                        <i class="i-majesticons-text"></i>
                        <span>{{ element.name }}</span>
                        <i class="i-majesticons-trash field-action"></i>
                      </div>
                    </template>
                  </draggable>
                </div>
              </div>

              <!-- 度量 -->
              <div class="field-group">
                <div class="field-group-header">
                  <i class="i-majesticons-folder"></i>
                  <span>度量</span>
                  <i class="i-majesticons-help"></i>
                </div>
                <div class="field-list">
                  <draggable v-model="dataFields.measures" :group="{ name: 'fieldss', pull: 'clone', put: false }"
                    :sort="false" :clone="cloneField" item-key="field">
                    <template #item="{ element }">
                      <div class="field-item" @click="addFieldToCalculation(element, 'measures')">
                        <i class="i-majesticons-calculator"></i>
                        <span>{{ element.name }}</span>
                        <i class="i-majesticons-trash field-action"></i>
                      </div>
                    </template>
                  </draggable>
                </div>
              </div>

              <!-- 时间 -->
              <div class="field-group">
                <div class="field-group-header">
                  <i class="i-majesticons-folder"></i>
                  <span>时间</span>
                  <i class="i-majesticons-help"></i>
                </div>
                <div class="field-list">
                  <draggable v-model="dataFields.time" :group="{ name: 'fields', pull: 'clone', put: false }"
                    :sort="false" :clone="cloneField" item-key="field">
                    <template #item="{ element }">
                      <div class="field-item" @click="addFieldToCalculation(element, 'dimensions')">
                        <i class="i-majesticons-calendar"></i>
                        <span>{{ element.name }}</span>
                        <i class="i-majesticons-trash field-action"></i>
                      </div>
                    </template>
                  </draggable>
                </div>
              </div>
            </div>

            <!-- 控件库 -->
            <!-- <div class="panel-section">
              <div class="section-header">
                <h3>控件库</h3>
              </div>
              <div class="control-list">
                <draggable v-model="controlPanelItems" :group="{ name: 'components', pull: 'clone', put: false }"
                  :clone="addComponentToCanvas" :sort="false" item-key="id">
                  <template #item="{ element }">
                    <div class="control-item" @click="addComponentToCanvas(element)">
                      <i :class="element.icon"></i>
                      <span>{{ element.name }}</span>
                    </div>
                  </template>
                </draggable>
              </div>
            </div> -->
          </div>
          <!-- 右侧待计算栏 -->
          <div class="right-panel">
            <div class="calculation-panel">
              <!-- 分析配置 -->
              <div class="calc-section">
                <div style="display: flex; align-items: center;margin-left: 16px; margin-top: 10px;">
                <p>图表：</p>
                 <el-select v-model="controlPanelItemsData" placeholder="选择生成的图表" style="width: 260px">
                  <el-option
                    v-for="item in controlPanelItems"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </div>
                <!-- 维度 -->
                <div class="calc-area">
                  <div class="area-header">
                    <span>维度</span>
                    <el-link type="primary" @click="openAggregation">聚合设置</el-link>
                  </div>
                  <div class="drop-zone dimensions-zone">
                    <draggable v-model="calculationPanel.dimensions" group="fields" @add="onFieldAdd"
                      @change="onFieldChange" item-key="id" class="drop-area">
                      <template #item="{ element, index }">
                        <div class="field-tag">
                          <span>{{ element.name }}</span>
                          <el-icon  @click="removeFromCalculation('dimensions', index)">
                            <Delete />
                          </el-icon>
                        </div>
                      </template>
                      <template #footer>
                        <div v-if="calculationPanel.dimensions.length === 0" class="drop-placeholder">
                          拖拽维度字段至此处
                        </div>
                      </template>
                    </draggable>
                  </div>
                </div>

                <!-- 过滤器 -->
                <div class="calc-area">
                  <div class="area-header">
                    <span>过滤器</span>
                  </div>
                  <div class="drop-zone filters-zone">
                    <draggable v-model="calculationPanel.filters" group="fieldss" @add="onFieldAdd"
                      @change="onFieldChange" item-key="id" class="drop-area">
                      <template #item="{ element, index }">
                        <div class="field-tag filter-tag">
                          <span>{{ element.name }}</span>
                          <i class="i-majesticons-filter"></i>
                          <el-icon  @click="removeFromCalculation('filters', index)">
                            <Delete />
                          </el-icon>
                          <el-icon  @click="openFilter">
                            <Tools />
                          </el-icon>
                        </div>
                      </template>
                      <template #footer>
                        <div v-if="calculationPanel.filters.length === 0" class="drop-placeholder">
                          拖拽数据字段至此处
                        </div>
                      </template>
                    </draggable>
                  </div>
                </div>
              </div>

              <!-- 结果展示 -->
              <div class="calc-section">
                <div class="result-config">
                  <div class="result-header">
                    <span>结果展示</span>
                    <el-radio-group v-model="generateConfig.showAll" size="small">
                      <el-radio :value="false">全部</el-radio>
                    </el-radio-group>
                    <el-input-number v-model="generateConfig.resultLimit" :min="1" :max="1000" size="small"
                      style="width: 100px;" />
                  </div>

                  <!-- 生成按钮 -->
                  <el-button type="primary" size="large" @click="generateAnalysis"
                    :disabled="calculationPanel.dimensions.length === 0 && calculationPanel.measures.length === 0"
                    class="generate-btn">
                    生成
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          <!-- 中间画布区域 -->
          <div class="canvas-area">
            <div class="canvas-header">
              <div class="canvas-title">设计画布</div>
              <div class="canvas-actions">
                <!-- <el-button size="small" type="danger" @click="clearCanvas">
                  <i class="i-majesticons-trash"></i>
                  清空画布
                </el-button> -->
              </div>
            </div>
            <div class="canvas-content">
              <showSmartCharts  :chartsData="canvasComponents[0]" style="height: 650px; width: 100%" />
              <!-- 空状态 -->
              <div v-if="canvasComponents.length === 0" class="empty-canvas">
                <div class="empty-content">
                  <i class="i-majesticons-box" style="font-size: 48px; color: #ccc;"></i>
                  <p>暂无数据</p>
                  <p>请从左侧拖拽控件到此处</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 预览模态窗口 -->
        <el-dialog v-model="previewVisible" :title="`${previewComponent?.name || '数据'} - 预览`" width="90%"
          :before-close="() => previewVisible = false">
          <div v-if="previewComponent" class="preview-content">
            <!-- 表格预览 -->
            <div v-if="previewComponent.type === 'table'" class="table-preview-content">
              <div class="preview-header">
                <div class="preview-info">
                  <el-tag type="info">{{ previewComponent.name }}</el-tag>
                  <span class="data-count">共 {{ previewPagination.total }} 条数据</span>
                </div>
                <div class="preview-actions">
                  <el-button size="small" @click="exportData">
                    <i class="i-majesticons-download"></i>
                    导出数据
                  </el-button>
                </div>
              </div>

              <el-table :data="previewTableData" style="width: 100%" max-height="500" stripe>
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="district" label="所属区县" width="100" />
                <el-table-column prop="street" label="所属街镇" width="120" />
                <el-table-column prop="village" label="所属村社" width="100" />
                <el-table-column prop="name" label="姓名" width="100" />
                <el-table-column prop="gender" label="性别" width="80" />
                <el-table-column prop="age" label="年龄" width="80" />
                <el-table-column prop="politicalStatus" label="政治面貌" width="100" />
                <el-table-column prop="visits" label="孕次" width="80" />
                <el-table-column prop="education" label="学历" width="80" />
                <el-table-column prop="occupation" label="职业" width="100" />
                <el-table-column prop="income" label="收入" width="100">
                  <template #default="scope">
                    {{ scope.row.income ? `${scope.row.income}元` : '-' }}
                  </template>
                </el-table-column>
              </el-table>

              <div class="preview-pagination">
                <el-pagination v-model:current-page="previewPagination.currentPage"
                  :page-size="previewPagination.pageSize" :total="previewPagination.total"
                  layout="total, prev, pager, next, jumper" @current-change="handlePreviewPageChange" />
              </div>
            </div>

            <!-- 图表预览 -->
            <div v-else-if="previewComponent.type === 'chart'" class="chart-preview-content">
              <div class="preview-header">
                <div class="preview-info">
                  <el-tag type="success">{{ previewComponent.name }}</el-tag>
                  <span class="data-count">{{ previewComponent.data.categories.length }} 个分类</span>
                </div>
              </div>

              <div class="chart-container">
                <ChartRenderer :data="previewComponent.data" :width="800" :height="400" />
              </div>

              <div class="chart-data-table">
                <h4>数据详情</h4>
                <el-table :data="getChartTableData()" style="width: 100%" size="small">
                  <el-table-column prop="category" label="分类" />
                  <el-table-column prop="value" label="数值" />
                  <el-table-column prop="percentage" label="占比" />
                </el-table>
              </div>
            </div>
          </div>
        </el-dialog>

        <!-- 聚合运算配置弹窗 -->
        <el-dialog v-model="aggregationVisible" title="聚合运算配置" width="600px">
          <el-form :model="aggregationForm" label-width="120px">
            <el-form-item label="度量数值列" required>
              <el-select v-model="aggregationForm.measureColumn" placeholder="请选择要计算的数值列">
                <el-option v-for="column in measureColumns" :key="column.value" :label="column.label"
                  :value="column.value" />
              </el-select>
            </el-form-item>

            <el-form-item label="运算方式" required>
              <el-select v-model="aggregationForm.operation" placeholder="请选择运算方式">
                <el-option v-for="option in operationOptions" :key="option.value" :label="option.label"
                  :value="option.value">
                  <div>
                    <div>{{ option.label }}</div>
                    <div style="font-size: 12px; color: #999;">{{ option.description }}</div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="分组列">
              <el-select v-model="aggregationForm.groupColumn" placeholder="可选：按某列分组统计" clearable>
                <el-option v-for="column in groupColumns" :key="column.value" :label="column.label"
                  :value="column.value" />
              </el-select>
              <div style="font-size: 12px; color: #666; margin-top: 4px;">
                选择分组列后将按该列的不同值分别计算结果
              </div>
            </el-form-item>

            <el-form-item label="结果名称" required>
              <el-input v-model="aggregationForm.resultName" placeholder="请输入运算结果的名称" maxlength="50" show-word-limit />
            </el-form-item>
          </el-form>
          <template #footer>
            <el-button @click="aggregationVisible = false">取消</el-button>
            <el-button type="primary" @click="confirmAggregation">开始计算</el-button>
          </template>
        </el-dialog>

        <!-- 运算结果展示弹窗 -->
        <el-dialog v-model="aggregationResultVisible" :title="aggregationResult?.title || '运算结果'" width="70%">
          <div v-if="aggregationResult" class="aggregation-result">
            <div class="result-header">
              <el-tag :type="aggregationResult.type === 'group' ? 'success' : 'info'">
                {{ aggregationResult.type === 'group' ? '分组统计' : '汇总统计' }}
              </el-tag>
              <span class="result-info">
                {{operationOptions.find(op => op.value === aggregationResult.operation)?.label}} -
                {{measureColumns.find(col => col.value === aggregationResult.measureColumn)?.label}}
                <span v-if="aggregationResult.groupColumn">
                  (按{{groupColumns.find(col => col.value === aggregationResult.groupColumn)?.label}}分组)
                </span>
              </span>
            </div>

            <!-- 简单聚合结果 -->
            <div v-if="aggregationResult.type === 'simple'" class="simple-result">
              <div class="result-value">
                {{ aggregationResult.value.toFixed(2) }}
              </div>
            </div>

            <!-- 分组聚合结果 -->
            <div v-else-if="aggregationResult.type === 'group'" class="group-result">
              <el-table :data="aggregationResult.data" style="width: 100%">
                <el-table-column prop="category"
                  :label="groupColumns.find(col => col.value === aggregationResult.groupColumn)?.label" />
                <el-table-column prop="value" label="计算结果">
                  <template #default="scope">
                    {{ scope.row.value.toFixed(2) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

          <template #footer>
            <el-button @click="closeAggregationResult">关闭</el-button>
            <el-button type="primary" @click="addResultToCanvas">添加到画布</el-button>
          </template>
        </el-dialog>

        <!-- 过滤器设置弹窗 -->
        <el-dialog v-model="filterVisible" title="设置过滤器" width="500px">
          <el-form :model="filterForm" label-width="100px">
            <el-form-item label="过滤方式:">
              <el-radio-group v-model="filterForm.filterMethod">
                <el-radio value="include">按条件过滤</el-radio>
                <el-radio value="exclude">按条件过滤</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="条件形式:">
              <el-radio-group v-model="filterForm.conditionType">
                <el-radio value="single">单条件</el-radio>
                <el-radio value="or">或条件</el-radio>
                <el-radio value="and">且条件</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="过滤条件:">
              <div class="filter-conditions">
                <div v-for="(condition, index) in filterForm.conditions" :key="index" class="condition-row">
                  <el-select v-model="condition.field" placeholder="精确匹配" style="width: 120px;">
                    <el-option label="精确匹配" value="exact" />
                    <el-option label="包含" value="contains" />
                    <el-option label="开始于" value="startsWith" />
                    <el-option label="结束于" value="endsWith" />
                  </el-select>
                  <el-input v-model="condition.value" placeholder="请输入字符或值" style="width: 200px; margin-left: 8px;" />
                  <el-button size="small" type="danger" @click="removeFilterCondition(index)" style="margin-left: 8px;">
                    删除
                  </el-button>
                </div>
                <el-button size="small" @click="addFilterCondition" style="margin-top: 8px;">
                  添加条件
                </el-button>
              </div>
            </el-form-item>
          </el-form>
          <template #footer>
            <el-button @click="filterVisible = false">取消</el-button>
            <el-button type="primary" @click="confirmFilter">确定</el-button>
          </template>
        </el-dialog>
      </div>
    </Block>
  </div>
</template>
<route>
	{
		meta: {
			title:'可视化引擎',
			ignoreLabel:false
		}
	}
</route>
<style scoped lang="scss">
.addAndEditAnalysis {
  .titleHeader {
    display: flex;
    align-items: center;

    &-text {
      margin-right: 0px;

      .box-item {
        width: 110px;
        margin-top: 10px;
      }
    }
  }

  .headerConter {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: center;

    &-number {
      width: 120px;
      display: flex;
      align-items: center;
      line-height: 22px;

      & span {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        margin-right: 6px;
        background: #c0c4cc;
      }
    }

    & .step {
      font-weight: 700;

      & span {
        width: 20px;
        height: 20px;
        color: #fff;
        background: rgb(23, 100, 206);
      }
    }
  }
}

.statistics-detail {
  display: flex;
  justify-content: center;
  background-color: #fff !important;
  // height: 100%;

  &-body {
    width: 880px;
    margin: 0 -10px;

    & .selectLedger {
      position: relative;

      &-main {
        height: 36px;
        display: flex;
        align-items: center;
        padding: 0px 10px;
        margin-bottom: 10px;
        border: 1px solid #dcdfe6;
        background: #f0f0f0;
        width: 300px;
        white-space: nowrap;
        /* 确保文本在一行内显示 */
        overflow: hidden;
        /* 超出容器部分的文本隐藏 */
        text-overflow: ellipsis;
        /* 使用省略号表示被截断的文本 */
      }

      & .analysis1 {
        width: 40px;
        position: absolute;
        top: 5px;
        left: 392px;
      }

      & .analysis2 {
        position: absolute;
        width: 154px;
        top: 27px;
        left: 392px;
      }

      & .analysis3 {
        position: absolute;
        width: 272px;
        top: 85px;
        left: 392px;
      }

      & .analysisButton1 {
        position: absolute;
        top: 18px;
        left: 428px;
      }

      & .analysisButton2 {
        position: absolute;
        top: 76px;
        left: 535px;
      }

      & .analysisButton3 {
        position: absolute;
        top: 142px;
        left: 650px;
      }
    }
  }
}

.statistics-table {
  &-main {
    margin-bottom: 10px;
  }

  .active1 {
    border-bottom: 2px solid transparent;
  }

  .active {
    border-bottom: 2px solid var(--el-color-primary);
    color: var(--el-color-primary);
  }
}

.preview-content {
  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 0;
    border-bottom: 1px solid #e4e7ed;

    .preview-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .data-count {
        font-weight: 500;
        color: #606266;
      }
    }

    .preview-actions {
      display: flex;
      gap: 8px;
    }
  }

  .table-preview-content {
    .preview-pagination {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }
  }

  .chart-preview-content {
    .chart-container {
      display: flex;
      justify-content: center;
      margin: 20px 0;
    }

    .chart-data-table {
      margin-top: 24px;

      h4 {
        margin-bottom: 12px;
        color: #303133;
        font-size: 16px;
      }
    }
  }
}

.aggregation-result {
  .result-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e4e7ed;

    .result-info {
      color: #606266;
      font-size: 14px;
    }
  }

  .simple-result {
    text-align: center;
    padding: 40px 0;

    .result-value {
      font-size: 48px;
      font-weight: bold;
      color: #409eff;
    }
  }

  .group-result {
    margin-top: 16px;
  }
}

.filter-conditions {
  .condition-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }
}

.engine-container {
  display: flex;
  height: calc(100vh - 260px);

  @media (max-width: 768px) {
    flex-direction: column;
    height: auto;
  }

  .left-panel {
    width: 300px;
    border-right: 1px solid #e4e7ed;
    background: #fafafa;
    overflow-y: auto;

    @media (max-width: 768px) {
      width: 100%;
      border-right: none;
      border-bottom: 1px solid #e4e7ed;
    }

    .panel-section {
      border-bottom: 1px solid #e4e7ed;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #f0f2f5;
        border-bottom: 1px solid #e4e7ed;

        h3 {
          margin: 0;
          font-size: 14px;
          font-weight: 500;
          color: #303133;
        }
      }

      .table-info-card {
        padding: 16px;
        background: white;
        margin: 8px;
        border-radius: 4px;
        border: 1px solid #e4e7ed;

        .table-name {
          font-weight: 500;
          color: #303133;
          margin-bottom: 8px;
        }

        .table-stats {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 12px;
          color: #666;
        }
      }

      .field-group {
        .field-group-header {
          display: flex;
          align-items: center;
          padding: 8px 16px;
          background: #f8f9fa;
          border-bottom: 1px solid #e4e7ed;

          i:first-child {
            margin-right: 8px;
            color: #606266;
          }

          span {
            flex: 1;
            font-size: 13px;
            font-weight: 500;
            color: #303133;
          }

          i:last-child {
            color: #909399;
            cursor: pointer;
          }
        }

        .field-list {
          max-height: 400px;
          overflow-y: auto;

          .field-item {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              background: #ecf5ff;

              .field-action {
                opacity: 1;
              }
            }

            i:first-child {
              margin-right: 8px;
              font-size: 14px;
              color: #606266;
            }

            span {
              flex: 1;
              font-size: 12px;
              color: #303133;
            }

            .field-action {
              opacity: 0;
              color: #f56c6c;
              cursor: pointer;
              transition: opacity 0.3s;
            }
          }
        }
      }

      .control-list {
        padding: 16px;

        .control-item {
          display: flex;
          align-items: center;
          padding: 12px;
          margin-bottom: 8px;
          background: white;
          border: 1px solid #e4e7ed;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            border-color: #409eff;
            background: #ecf5ff;
          }

          i {
            margin-right: 8px;
            font-size: 16px;
            color: #606266;
          }

          span {
            font-size: 14px;
            color: #303133;
          }
        }
      }
    }
  }

  .canvas-area {
    flex: 1;
    display: flex;
    flex-direction: column;

    @media (max-width: 768px) {
      min-height: 400px;
    }

    .canvas-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #e4e7ed;
      background: white;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
      }

      .canvas-title {
        font-size: 16px;
        font-weight: 500;
      }

      .canvas-actions {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;

        @media (max-width: 768px) {
          justify-content: center;
        }
      }
    }

    .canvas-content {
      flex: 1;
      padding: 16px;
      background: #f5f5f5;
      position: relative;
      overflow: auto;

      .canvas-draggable {
        min-height: 100%;
      }

      .canvas-component {
        position: relative;
        margin-bottom: 16px;
        background: white;
        border: 2px solid transparent;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          border-color: #409eff;
        }

        &.active {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        .component-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          border-bottom: 1px solid #e4e7ed;
          background: #fafafa;
          border-radius: 6px 6px 0 0;

          span {
            font-weight: 500;
          }

          .component-actions {
            display: flex;
            gap: 4px;
          }
        }

        .component-content {
          padding: 16px;
          min-height: 250px;

          .table-preview {
            .table-info {
              text-align: center;
              color: #606266;
              font-size: 14px;
              margin-bottom: 12px;
            }

            .table-sample {
              margin-top: 8px;
            }
          }

          .chart-preview {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
          }
        }
      }

      .empty-canvas {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;

        .empty-content {
          p {
            margin: 8px 0;
            color: #909399;
          }
        }
      }
    }
  }

  .right-panel {
    width: 350px;
    border-left: 1px solid #e4e7ed;
    background: #fafafa;
    overflow-y: auto;
    @media (max-width: 768px) {
      width: 100%;
      border-left: none;
      border-top: 1px solid #e4e7ed;
    }

    .calculation-panel {
      .calc-section {
        border-bottom: 1px solid #e4e7ed;

        .calc-header {
          display: flex;
          align-items: center;
          padding: 12px 16px;
          background: #f0f2f5;
          border-bottom: 1px solid #e4e7ed;

          .step-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            background: #409eff;
            color: white;
            border-radius: 50%;
            font-size: 12px;
            font-weight: bold;
            margin-right: 8px;
          }

          .step-title {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
          }
        }

        .calc-area {
          padding: 16px;

          .area-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            span {
              font-size: 13px;
              font-weight: 500;
              color: #303133;
            }
          }

          .drop-zone {
            min-height: 240px;
            border: 2px dashed #d9d9d9;
            border-radius: 6px;
            background: #fafafa;
            transition: all 0.3s;

            &:hover {
              border-color: #409eff;
              background: #ecf5ff;
            }

            .drop-area {
              min-height: 240px;
              padding: 8px;

              .field-tag {
                display: inline-flex;
                align-items: center;
                padding: 4px 8px;
                margin: 2px;
                background: #e1f3d8;
                border: 1px solid #b3d8a4;
                border-radius: 4px;
                font-size: 12px;
                color: #67c23a;

                &.measure-tag {
                  background: #ecf5ff;
                  border-color: #b3d8ff;
                  color: #409eff;
                }

                &.filter-tag {
                  background: #fdf6ec;
                  border-color: #f5dab1;
                  color: #e6a23c;
                }

                span {
                  margin-right: 4px;
                }

                i {
                  cursor: pointer;
                  margin-left: 4px;

                  &:hover {
                    color: #f56c6c;
                  }
                }
              }

              .drop-placeholder {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 60px;
                color: #999;
                font-size: 12px;
              }
            }
          }
        }

        .result-config {
          padding: 16px;

          .result-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 16px;
            font-size: 13px;

            span {
              color: #303133;
            }
          }

          .generate-btn {
            width: 100%;
            height: 40px;
            font-size: 16px;
            font-weight: bold;
          }
        }
      }
    }
  }
}
</style>
