<script setup lang="ts" name="dataanalysis">
import {onMounted, ref} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import {
	ledgerDataAnalysis,
	ledgerDataAnalysisBatchDelete,
	getledgerDataAnalysis,
	getLedgerStatistics,
	deleteLedgerStatisticsById,
	downloadLedgerStatistics,
} from '@/api/LedgerApi'
import {ElMessage, ElMessageBox} from 'element-plus'
import {useUserStore} from '@/stores/useUserStore'
import {updateLabelTitle} from '@/hooks/useLabels'
import { mockDataService, type TableRow, type ChartData, dataList } from './mockDataService'
const loading = ref(false)
const route = useRoute()
const router = useRouter()
import {USER_ROLES_ENUM} from '@/define/organization.define'
const currentUser = ref(JSON.parse(localStorage.getItem('currentUserInfo') as string))
// 数据管理岗位权限
const role = ref(currentUser.value.staffRole.some((item) => item === USER_ROLES_ENUM.DATA_LEADER))
const chartType = ref([
	{name: '统计表', id: 0},
	{name: '柱状图', id: 1},
	{name: '桑葚图', id: 2},
	{name: '环形图', id: 3},
	{name: '饼图', id: 4},
	{name: '折线图', id: 5},
])
// 表格中的操作列
const buttons: any = [
	{code: 'view', label: '查看', icon: 'i-majesticons-eye-line', verify: 'true'},
	{
		code: 'edit',
		label: '编辑',
		type: 'primary',
		icon: 'i-majesticons-pencil-alt-line',
		verify: 'true',
		disabled: `${!role.value}`,
	},
	// {
	// 	code: 'download',
	// 	label: '下载',
	// 	type: 'info',
	// 	icon: 'i-ic-baseline-download',
	// 	verify: 'true',
	// },
	{
		code: 'delete',
		label: '删除',
		type: 'danger',
		icon: 'i-ic-round-dangerous',
		verify: 'true',
		disabled: `${!role.value}`,
	},
]
// 表格中的操作列
const onTableClickButton = ({btn, row, index}: any) => {
	if (btn.code === 'view') {
		router.push({path: '/visualizationEngine/addAndEditAnalysis', query: {id: row.tableInfoId}})
	}

	if (btn.code === 'edit') {
		router.push({path: '/visualizationEngine/addAndEditAnalysis', query: {id: row.tableInfoId}})
	}

	if (btn.code === 'delete') {
		let data: any = [row.id]
		ElMessageBox.confirm(`是否要删除 ${row.name} ?`, '消息确认', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
			.then(() => {
				mockDataService.dataList.forEach((it,index) =>{
					if(row.tableInfoId ==it.tableInfoId){
						mockDataService.dataList.splice(index,1)
					}
				})
				ElMessage.success('删除成功！')
				console.log( mockDataService.dataList);
				
					getList()
				// ledgerDataAnalysisBatchDelete(data).then(() => {
				// 	ElMessage.success('删除成功！')
				// 	getList()
				// })
			})
			.catch(() => {})
	}

	if (btn.code === 'download') {
		console.log(123);
		
		// downloadLedgerStatistics(scope.id)
	}
}
// 查询条件
const runwayForm: any = ref({
	name: '',
	chartType: null,
})
// 设置表格高度计算
const tableHeight = ref(0)
const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}
// 多选数据
const statisticsList: any = ref([])
const selectedCount = ref(0)
const selectionChange = (selection: []) => {
	selectedCount.value = selection.length
	statisticsList.value = selection
	console.log(statisticsList.value)
}

const addAnalysis = (e: any) => {
	router.push({path: '/visualizationEngine/addAndEditAnalysis'})
	// if (e === 'a') {
	// 	router.push({path: '/visualizationEngine/addAndEditAnalysis'})
	// }
}

// 表中的内容
const tableData = ref([])
// 表头
const colData: any = ref([
	{
		prop: 'name',
		label: '可视化名称',
		tooltip: true,
	},
	{
		prop: 'echart',
		label: '图表类型',
	},

	{
		prop: 'creationTime',
		label: '更新时间',
	},
])
const active = ref(0)
const url = ref('')

//表格与分页关联
const reqParams = ref({
	skipCount: 0,
	maxResultCount: 10,
})
// 分页相关参数
const pagination = ref({
	total: 0,
	page: 1,
	size: 10,
})
// 查询
const getList = () => {
	loading.value = true
	let dataList =  JSON.parse(JSON.stringify(mockDataService.dataList))
	if(runwayForm.value.name){
		let data:any = []
		dataList.forEach(item =>{
			if(runwayForm.value.name == item.name){
				data.push(item)
			}
		})
		tableData.value = data
	}else{
		tableData.value = dataList
	}
	loading.value = false
	pagination.value.total = dataList.length
	// ledgerDataAnalysis({
	// 	skipCount: (pagination.value.page - 1) * pagination.value.size,
	// 	MaxResultCount: pagination.value.size,
	// 	name: runwayForm.value.name,
	// 	chartType: runwayForm.value.chartType,
	// }).then((res: any) => {
	// 	loading.value = false
	// 	tableData.value = res.data.items
	// 	pagination.value.total = res.data.totalCount
	// })
}
// 批量删除
const ledgerArrDelete = () => {
	if(selectedCount.value == 0){
		ElMessage.warning('请选择数据！')
		return
	}
	let data: any = []
	statisticsList.value.forEach((item: any, index: any) => {
		data.push(item.tableInfoId)
	})

	ElMessageBox.confirm('是否要批量删除?', '消息确认', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			data.forEach(item =>{
				mockDataService.dataList.forEach((it,index) =>{
					if(item ==it.tableInfoId){
						mockDataService.dataList.splice(index,1)
					}
				})
			})
			selectedCount.value = 0
			getList()
			// ledgerDataAnalysisBatchDelete(data).then(() => {
			// 	ElMessage.success('删除成功！')
			// 	selectedCount.value = 0
			// 	getList()
			// })
		})
		.catch(() => {})
}
// 高级查询
const seniorList = () => {
	pagination.value.page = 1
	pagination.value.size = 10
	getList()
}
// 清空
const empty = () => {
	runwayForm.value.name = ''
	runwayForm.value.chartType = null
	getList()
}
// 分页事件
const onPaginationChange = (val: any, type: any) => {
	if (type === 'page') {
		pagination.value.page = val
		reqParams.value.skipCount = (val - 1) * pagination.value.size
	} else {
		pagination.value.page = 1
		pagination.value.size = val
		reqParams.value.maxResultCount = pagination.value.size
	}
	getList()
}
const onTableBeforeComplete = ({items, next}: any) => {
	const temp: any = []
	if (active.value === 1) {
		items.forEach((item: any) => {
			item.details.forEach((detail: any) => {
				temp.push({
					district: item.district,
					departmentName: detail.departmentName,
					ledgerCount: detail.ledgerCount,
					ledgerDataCount: detail.ledgerDataCount,
				})
			})
		})
		next(temp)
	} else if (active.value == 2) {
		items.forEach((item: any) => {
			item.details.forEach((detail: any) => {
				temp.push({
					district: item.district,
					street: detail.street,
					cityLedgerCount: detail.cityLedgerCount,
					cityLedgerDataCount: detail.cityLedgerDataCount,
					districtLedgerCount: detail.districtLedgerCount,
					districtLedgerDataCount: detail.districtLedgerDataCount,
					historyReportCount: detail.historyReportCount,
					ledgerCount: detail.ledgerCount,
					ledgerDataCount: detail.ledgerDataCount,
					userCount: detail.userCount,
				})
			})
		})
		next(temp)
	} else {
		next(items)
	}
}
onMounted(() => {
	getList()
})
</script>

<route>
	{
		meta: {
			title:'可视化引擎',
			ignoreLabel:false
		}
	}
</route>
<template>
	<div class="dataAnalysis">
		<Block
			:enable-fixed-height="true"
			:enable-expand-content="true"
			:enableBackButton="false"
			:title="'可视化引擎'"
			@heightChanged="onBlockHeightChanged"
		>
			<template #topRight>
				<el-button
					type="primary"
					size="small"
					@click="addAnalysis"
					style="margin-left: 10px"
				>
					新建可视化
				</el-button>
				<!-- <el-dropdown @command="addAnalysis" v-if="role">
					<el-button type="primary" size="small">
						新建分析<el-icon class="el-icon--right"><arrow-down /></el-icon>
					</el-button>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item command="a">空白新建</el-dropdown-item>
							<el-dropdown-item command="b">从模版新建</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown> -->
				<el-button
					type="danger"
					size="small"
					@click="ledgerArrDelete"
					style="margin-left: 10px"
				>
					批量删除
				</el-button>
			</template>
			<template #expand>
				<div class="search-box" v-action:enter="seniorList">
					<el-input
						placeholder="请输入可视化名称"
						v-model="runwayForm.name"
						style="width: 250px; margin-right: 10px"
					></el-input>
					<!-- <el-select
						v-model="runwayForm.chartType"
						filterable
						clearable
						@change="seniorList"
						placeholder="请选择统计表"
						style="width: 250px; margin-right: 10px"
					>
						<el-option
							v-for="(item, index) in chartType"
							:key="index"
							:label="item?.name"
							:value="item?.id"
						/>
					</el-select> -->

					<el-button size="default" type="default" @click="empty">
						<i class="i-ic-baseline-cleaning-services" style="margin-right: 3px"></i>
						清空
					</el-button>
					<el-button size="default" type="primary" @click="seniorList">
						<i class="i-ic-baseline-send" style="margin-right: 3px"></i>
						查询</el-button
					>
				</div>
			</template>
			<TableV2
				ref="tableRef"
				:loading="loading"
				:height="tableHeight"
				:columns="colData"
				:defaultTableData="tableData"
				:headers="{Urlkey: 'ledger'}"
				:enableToolbar="false"
				:enable-create="false"
				:enable-edit="false"
				:enable-delete="false"
				:enableSelection="true"
				:enableIndex="false"
				@before-complete="onTableBeforeComplete"
				:req-params="reqParams"
				:buttons="buttons"
				@clickButton="onTableClickButton"
				@selection-change="selectionChange"
			>
				<!-- <template #name="scope">
					<span
						class="link-click"
						@click="
							$router.push({path: '/visualizationEngine/viewAnalysis', query: {id: scope.row.id}})
						"
						>{{ scope.row.name }}</span
					>
				</template> -->
				<template #chartType="scope">
					<div>
						{{
							scope.row.chartType == 0
								? '统计表'
								: scope.row.chartType == 1
								? '柱状图'
								: scope.row.chartType == 2
								? '桑葚图'
								: scope.row.chartType == 3
								? '环形图'
								: scope.row.chartType == 4
								? '饼图'
								: scope.row.chartType == 5
								? '折线图'
								: ''
						}}
					</div>
				</template>
			</TableV2>
			<Pagination
				:total="pagination.total"
				:page-size="pagination.size"
				:current-page="pagination.page"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			>
			</Pagination>
		</Block>
	</div>
</template>
<style scoped lang="scss">
.search-box {
	align-items: start;
	display: flex;
	padding: 10px 15px;
}
</style>
