// 数据模拟服务
export interface MockDataConfig {
  type: 'table' | 'chart'
  chartType?: 'bar' | 'pie' | 'line'
  count?: number
}

export interface TableRow {
  id: number
  district: string
  street: string
  village: string
  name: string
  gender: string
  age: number
  idCard: string
  politicalStatus: string
  visits: number
  birthDate: string
  income?: number
  education?: string
  maritalStatus?: string
  occupation?: string
}

export interface ChartData {
  categories: string[]
  values: number[]
  chartType: string
}

// 基础数据池
const dataPool = {
  districts: ['万州区', '永川区', '大足区', '璧山区', '江津区', '合川区', '南川区', '綦江区', '铜梁区', '潼南区', '荣昌区', '开州区'],
  streets: ['太白街道', '中山街道', '南门街道', '北门街道', '东门街道', '西门街道', '新华街道', '人民街道', '解放街道', '建设街道', '胜利街道', '和平街道'],
  villages: ['幸福村', '和谐村', '富强村', '民主村', '文明村', '团结村', '进步村', '发展村', '新农村', '美丽村', '小康村', '振兴村'],
  surnames: ['张', '王', '李', '赵', '刘', '陈', '杨', '黄', '周', '吴', '徐', '孙', '马', '朱', '胡', '郭', '何', '高', '林', '罗', '宋', '谢', '唐', '韩', '冯', '于', '董', '萧', '程'],
  givenNames: ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀英', '霞', '平', '华', '建', '国', '文', '玲', '红', '燕', '鹏', '辉', '宇'],
  politicalStatus: ['中共党员', '群众', '共青团员', '民主党派', '无党派人士'],
  education: ['小学', '初中', '高中/中专', '大专', '本科', '硕士研究生', '博士研究生'],
  maritalStatus: ['未婚', '已婚', '离异', '丧偶', '再婚'],
  occupation: ['农民', '工人', '教师', '医生', '公务员', '个体户', '企业员工', '退休人员', '学生', '自由职业者', '技术人员', '管理人员'],
  incomeRanges: [
    { min: 20000, max: 35000, weight: 0.3 }, // 低收入
    { min: 35000, max: 60000, weight: 0.4 }, // 中等收入
    { min: 60000, max: 100000, weight: 0.2 }, // 中高收入
    { min: 100000, max: 200000, weight: 0.08 }, // 高收入
    { min: 200000, max: 500000, weight: 0.02 } // 超高收入
  ]
}

// 生成随机姓名
const generateRandomName = (): string => {
  const surname = dataPool.surnames[Math.floor(Math.random() * dataPool.surnames.length)]
  const givenName = dataPool.givenNames[Math.floor(Math.random() * dataPool.givenNames.length)]
  return surname + givenName
}

// 生成随机身份证号
const generateIdCard = (): string => {
  const prefix = '361123'
  const year = 1960 + Math.floor(Math.random() * 40)
  const month = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')
  const day = String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')
  const suffix = Math.random().toString().substr(2, 3)
  return `${prefix}${year}${month}${day}***${suffix}`
}

// 生成随机日期
const generateBirthDate = (age: number): string => {
  const currentYear = new Date().getFullYear()
  const birthYear = currentYear - age
  const month = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')
  const day = String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')
  return `${birthYear}-${month}-${day}`
}

// 根据权重生成收入
const generateIncome = (age: number, education: string, occupation: string): number => {
  // 基础收入范围选择
  let selectedRange = dataPool.incomeRanges[0]
  const random = Math.random()
  let cumulativeWeight = 0

  for (const range of dataPool.incomeRanges) {
    cumulativeWeight += range.weight
    if (random <= cumulativeWeight) {
      selectedRange = range
      break
    }
  }

  // 根据年龄、学历、职业调整收入
  let baseIncome = Math.floor(Math.random() * (selectedRange.max - selectedRange.min)) + selectedRange.min

  // 年龄调整（经验加成）
  if (age >= 30 && age <= 50) {
    baseIncome *= 1.1 // 黄金年龄段加成
  } else if (age > 50) {
    baseIncome *= 0.95 // 年龄较大略减
  }

  // 学历调整
  const educationMultiplier = {
    '小学': 0.8,
    '初中': 0.85,
    '高中/中专': 0.9,
    '大专': 1.0,
    '本科': 1.15,
    '硕士研究生': 1.3,
    '博士研究生': 1.5
  }
  baseIncome *= educationMultiplier[education as keyof typeof educationMultiplier] || 1.0

  // 职业调整
  const occupationMultiplier = {
    '农民': 0.7,
    '工人': 0.85,
    '个体户': 0.9,
    '企业员工': 1.0,
    '技术人员': 1.2,
    '教师': 1.1,
    '医生': 1.25,
    '管理人员': 1.3,
    '公务员': 1.15,
    '自由职业者': 0.95,
    '退休人员': 0.6,
    '学生': 0.1
  }
  baseIncome *= occupationMultiplier[occupation as keyof typeof occupationMultiplier] || 1.0

  return Math.floor(baseIncome)
}

// 生成表格数据
export const generateTableData = (count: number = 50): TableRow[] => {
  const data: TableRow[] = []

  for (let i = 0; i < count; i++) {
    const age = Math.floor(Math.random() * 60) + 20
    const gender = Math.random() > 0.5 ? '男' : '女'
    const education = dataPool.education[Math.floor(Math.random() * dataPool.education.length)]
    const occupation = dataPool.occupation[Math.floor(Math.random() * dataPool.occupation.length)]

    // 根据年龄调整婚姻状况概率
    let maritalStatus = dataPool.maritalStatus[Math.floor(Math.random() * dataPool.maritalStatus.length)]
    if (age < 25) {
      maritalStatus = Math.random() > 0.7 ? '已婚' : '未婚' // 年轻人更多未婚
    } else if (age > 60) {
      maritalStatus = Math.random() > 0.1 ? '已婚' : (Math.random() > 0.5 ? '丧偶' : '离异') // 老年人更多已婚或丧偶
    }

    data.push({
      id: i + 1,
      district: dataPool.districts[Math.floor(Math.random() * dataPool.districts.length)],
      street: dataPool.streets[Math.floor(Math.random() * dataPool.streets.length)],
      village: dataPool.villages[Math.floor(Math.random() * dataPool.villages.length)],
      name: generateRandomName(),
      gender,
      age,
      idCard: generateIdCard(),
      politicalStatus: dataPool.politicalStatus[Math.floor(Math.random() * dataPool.politicalStatus.length)],
      visits: Math.floor(Math.random() * 10) + 1,
      birthDate: generateBirthDate(age),
      income: generateIncome(age, education, occupation),
      education,
      maritalStatus,
      occupation
    })
  }

  return data
}

// 生成图表数据
export const generateChartData = (chartType: string): ChartData => {
  let categories: string[] = []
  let values: number[] = []

  switch (chartType) {
    case 'bar':
    case 'line':
      categories = dataPool.districts.slice(0, 8) // 取8个区县
      values = categories.map((_, index) => {
        // 模拟真实的人口分布，中心城区人口更多
        const baseValue = Math.floor(Math.random() * 50) + 30
        const centerBonus = index < 4 ? Math.floor(Math.random() * 30) + 20 : 0
        return baseValue + centerBonus
      })
      break
    case 'pie':
      categories = ['男', '女']
      values = [52, 48] // 模拟真实的性别比例
      break
    default:
      categories = dataPool.districts.slice(0, 6)
      values = categories.map(() => Math.floor(Math.random() * 100) + 10)
  }

  return {
    categories,
    values,
    chartType
  }
}

// 聚合运算函数
export const performAggregation = (data: TableRow[], column: string, operation: string): number => {
  const values = data.map(row => {
    switch (column) {
      case 'age':
        return row.age
      case 'visits':
        return row.visits
      case 'income':
        return row.income || 0
      default:
        return 0
    }
  }).filter(val => val > 0)

  switch (operation) {
    case 'sum':
      return values.reduce((sum, val) => sum + val, 0)
    case 'average':
      return values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0
    case 'count':
      return values.length
    case 'distinctCount':
      return new Set(values).size
    case 'max':
      return values.length > 0 ? Math.max(...values) : 0
    case 'min':
      return values.length > 0 ? Math.min(...values) : 0
    default:
      return 0
  }
}

// 按分组聚合数据
export const aggregateByGroup = (data: TableRow[], groupColumn: string, valueColumn: string, operation: string) => {
  const groups: { [key: string]: TableRow[] } = {}
  
  // 分组
  data.forEach(row => {
    const groupKey = row[groupColumn as keyof TableRow] as string
    if (!groups[groupKey]) {
      groups[groupKey] = []
    }
    groups[groupKey].push(row)
  })
  
  // 聚合
  const result = Object.keys(groups).map(groupKey => ({
    category: groupKey,
    value: performAggregation(groups[groupKey], valueColumn, operation)
  }))
  
  return result
}

// 生成业务场景数据
export const generateBusinessScenarioData = (scenario: string) => {
  switch (scenario) {
    case 'population':
      return {
        title: '人口统计分析',
        data: generateTableData(100),
        charts: [
          { type: 'bar', title: '各区县人口分布', data: generateChartData('bar') },
          { type: 'pie', title: '性别比例', data: generateChartData('pie') }
        ]
      }
    case 'agriculture':
      return {
        title: '农膜使用台账',
        data: generateTableData(80).map(row => ({
          ...row,
          filmType: ['PE膜', 'PVC膜', '生物降解膜'][Math.floor(Math.random() * 3)],
          usage: Math.floor(Math.random() * 1000) + 100,
          recycleRate: Math.floor(Math.random() * 100)
        })),
        charts: [
          { type: 'line', title: '农膜使用趋势', data: generateChartData('line') }
        ]
      }
    default:
      return {
        title: '综合数据分析',
        data: generateTableData(50),
        charts: [
          { type: 'bar', title: '数据分布', data: generateChartData('bar') }
        ]
      }
  }
}
export const dataList =[
  {
    "name": "123",
    "analysisDescription": "123",
    "tableInfoId": "3a19028f-d213-b470-e5e3-c708ec43649c",
    "canvasComponents": [
        {
            "echartType": 1,
            "chartCoordinateYList": [
                7,
                8,
                5,
                1
            ],
            "chartCoordinateXList": [
                "硕士",
                "博士",
                "本科",
                "大专"
            ]
        }
    ],
    "controlPanelItemsData": "bar-chart",
    "filters": [
        {
            "name": "No.孕次",
            "field": "visits",
            "type": "number",
            "id": "visits_1750244591292"
        }
    ],
    "dimensions": [
        {
            "name": "str.身份证号",
            "field": "idCard",
            "type": "string",
            "id": "idCard_1750244589902"
        }
    ],
    "creationTime": "2025-06-18 19:03:20",
    "echart": "柱状图"
}
]

// 导出主要函数
export const mockDataService = {
  generateTableData,
  generateChartData,
  performAggregation,
  aggregateByGroup,
  generateBusinessScenarioData,
  dataList
}
