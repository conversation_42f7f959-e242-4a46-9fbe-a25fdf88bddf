<script lang="ts" setup>
import {reactive,onBeforeUpdate, onMounted, onUnmounted, ref, watch,markRaw} from 'vue'
import * as echarts from 'echarts';
import {ElNotification, ElMessage} from 'element-plus'
import { it } from 'element-plus/es/locale';

interface Props {
	chartsData?:any 
}
const props = withDefaults(defineProps<Props>(), {
	chartsData: {} as any,
})

const eventType:any = ref(null)
const echartsOneList:any = ref([])
const onetotal = ref(16)
const echartsThreeOne = ref([7,8,5,1])
const echartsThreeData = ref(["硕士", "博士","本科","大专"])
// 饼图
const showSmartChartsPie = () => {
  let dom = document.getElementById("showSmartCharts");
  eventType.value = echarts.init(dom);
  let list = echartsOneList.value;
  // 设置 静态之后去除
  // let charts1values = [2,189,1,20,36]
  // let baifenbi = ['0.8%','79.4%','0.4%','8.4%','15.1%']
  list.forEach((item:any,index:any)=>{
    item.ratio = ( item.value * 100  / onetotal.value).toFixed(2)
  })

  let option = {
    // title: {
    //   text: `总人数`,
    //   subtext: `{a|${this.onetotal}}{b| 人}`,
    //   x: "284", // 标题水平居中
    //   y: "94", // 标题垂直居中
    //   textAlign: "center",
    //   textStyle: {
    //     color: "#FFFFFF",
    //     fontWeight: "400",
    //     fontSize: 16,
    //     width: "200",
    //     align: "center",
    //   },
    //   subtextStyle: {
    //     rich: {
    //       a: {
    //         fontSize: 24,
    //         color: "#FFFFFF",
    //         fontWeight: "400",
    //         textShadowColor: "#0077FF",
    //         textShadowBlur: 6,
    //         textShadowOffsetX: 1,
    //         textShadowOffsetY: 3,
    //       },
    //       b: {
    //         fontSize: 12,
    //         color: "#818B94",
    //       },
    //     },
    //   },
    // },
    tooltip: {
      show: false,
      trigger: "item",
    },

    color: ["#4095ED", "#136BF0", "#637FEA", "#B35AFF", "#15C29F"],

    legend: {
      bottom: "10px",
      left: "center",
      icon: "circle",
      itemGap: 20,
      textStyle: {
        color: "#333",
        fontSize: 14,
        padding: [2, 0, 0, 0],
        rich: {
          a: {
            verticalAlign: "middle",
          },
        },
      },
    },

    series: [
      {
        name: "Access From",
        type: "pie",
        radius: ["42%", "50%"],
        bottom: "12%",

        avoidLabelOverlap: false,
        itemStyle: {
          normal: {
            borderRadius: 17,
            borderWidth: 2,
            borderColor: "#061c1e",
            labelLine: {
              length: 30,
              length2: 70, // 设置标签线的长度为 10px
            },
            label: {
              formatter: function (val:any) {
                return `{a|${val.value}}{c|}{b${val.dataIndex}|${val.percent}%} \n\n`;
                // `{a|${val.value}}{c|人}{b${val.dataIndex}|${val.percent}%} \n\n`
              },
              padding: [0, -70],

              rich: {
                a: {
                  color: "#333",
                  fontSize: 16,
                  lineHeight: 20,
                  fontFamily:'YouSheBiaoTiHei'
                },
                c: {
                  color: "#818B94",
                  fontSize: 12,
                  lineHeight: 20,
                  padding: [0, 10, 0, 0],
                },
                b0: {
                  fontSize: 14,
                  lineHeight: 20,
                  color: "#4095ED",
                },
                b1: {
                  fontSize: 14,
                  lineHeight: 20,
                  color: "#136BF0",
                },
                b2: {
                  fontSize: 14,
                  lineHeight: 20,
                  color: "#637FEA",
                },
                b3: {
                  fontSize: 14,
                  lineHeight: 20,
                  color: "#B35AFF",
                },
                b4: {
                  fontSize: 14,
                  lineHeight: 20,
                  color: "#15C29F",
                },
              },
            },
          },
        },
        emphasis: {
          scale: false,
          label: {
            show: true,
          },
        },
        // labelLine: {
        //   show: false,
        // },
        data: list,
      },
    ],
  };
  eventType.value.setOption(option);
}
// 柱状图
const showSmartChartsBar= () => {
  let dom = document.getElementById("showSmartCharts");
  // 后续删除
  eventType.value = markRaw(echarts.init(dom));
  let option = {
    color: [
      {
        type: "linear",
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          { offset: 0.2, color: "#58CFFF" },
          { offset: 1, color: "rgba(255, 255, 255, 0)" },
        ],
      },
      {
        type: "linear",
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          { offset: 0.2, color: "#E1B661" },
          { offset: 1, color: "rgba(255, 255, 255, 0)" },
        ],
      },
      {
        type: "linear",
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          { offset: 0.2, color: "#58CFFF" },
          { offset: 1, color: "rgba(255, 255, 255, 0)" },
        ],
      },
      {
        type: "linear",
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          { offset: 0.2, color: "#61CD82" },
          { offset: 1, color: "rgba(255, 255, 255, 0)" },
        ],
      },
    ],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    dataset: {
      source: [
        echartsThreeData.value,
        echartsThreeOne.value,
      ],
    },
    grid: {
      top: "85px",
      left: "2%",
      right: "2%",
      bottom: `${(isMax.value * 12) + 10} px`,
    },
    legend: {
      left: "center",
      top: "20px",
      data: [

      ],
      textStyle: {
        color: "#333",
      },
    },
    xAxis: {
      type: "category",
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          type: "solid", // 设置刻度线为虚线
          color: "#B8D3F1", // 设置刻度线颜色
        },
      },
      axisLabel: {
        rotate: 45,  // 设置x轴文本倾斜45度
        interval: 0, // 设置标签间隔为0，确保每个标签都显示
        margin: 10,
      },
    },
    yAxis: {
      type: "value",
      // name: "(人)",
      nameTextStyle: {
        align: "right",
        padding: [0, 7, 0, 0],
      },
      splitLine: {
        lineStyle: {
          color: "#818B94",
          type: [4, 2],
        },
      },
      axisLine: {
        lineStyle: {
          type: "solid", // 设置刻度线为虚线
          color: "#B8D3F1", // 设置刻度线颜色
        },
      },
    },
    series: [
      {
        type: "bar",
        seriesLayoutBy: "row",
        barWidth: 15, // 设置柱状图的宽度
        barGap: "60%", // 设置柱状图的间隔
        label: {
          normal: {
            show: true,//开启显示
            position: 'top',//柱形上方
            textStyle: { //数值样式
              fontFamily: 'YouSheBiaoTiHei',
              color: '#333',
              fontWeight: 'bold',
              fontSize: 20,
            }
          }
        }
      },
    ],
    dataZoom: {
      type: 'slider', // 滑动条类型
      moveOnMouseWheel:false,
      show: echartsThreeData.value.length > 20 ? true : false, // 是否显示滑动条,在这里可以根据自己的需求做判断，超过时显示进度条
      startValue: 0, // 展示区域内容的起始数值
      endValue: 20, // 展示区域内容的结束数值
      height: 6, // 滑动条组件高度
      bottom: 1, // 距离图表区域下边的距离
      showDetail: false, // 拖拽时是否显示详情
      showDataShadow: false, // 是否在组件中显示数据阴影
      fillerColor: '#dbdee5', // 平移条的填充颜色  
      borderColor: 'transparent', // 边框颜色
      zoomLock: true, // 锁定视图
      brushSelect: false, // 不可缩放 滑动条默认是有手柄可以进行展示的内容区域缩放的，不太美观
      // 通过该属性可以只滑动，不显示缩放功能
      handleStyle: {
        // 手柄样式
        opacity: 0
      }
    },
  };
  eventType.value.setOption(option);
}
const isMax = ref(0)
onMounted(() => {
  if(props.chartsData?.echartType == 1){
    echartsThreeOne.value = props.chartsData.chartCoordinateYList
    echartsThreeData.value = props.chartsData.chartCoordinateXList
    let Max:any = []
    echartsThreeData.value.forEach((item) =>{
      Max.push(item.length) 
    })
    isMax.value =  Math.max(...Max);
    showSmartChartsBar()
  }else if(props.chartsData?.echartType == 2){
    props.chartsData.chartCoordinateYList.forEach((item:any) =>{
      onetotal.value +=item
      echartsOneList.value.push( { value: item, name: "", ratio: "0" },)
    })
    props.chartsData.chartCoordinateXList.forEach((item:any,index:any) =>{
      echartsOneList.value[index].name = item
    })
    showSmartChartsPie()
  }
})
onUnmounted(() => {})

onActivated(() => {})
</script>
<template>
	<div id="showSmartCharts"></div>
</template>
<style scoped lang="scss">
.screenData {
	height: 100%;
}
</style>
