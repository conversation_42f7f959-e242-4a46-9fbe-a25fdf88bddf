<script setup lang="ts" name="dataSourceManagement">
// 数据源管理页面
import { nextTick, ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { ArrowDown, ArrowRight, Folder, Loading } from '@element-plus/icons-vue'

// 注意：Dialog、Form、BaseTableComp 等组件已全局注册，无需导入
import * as echarts from 'echarts'
import IntegrationStrategyDialog from './components/IntegrationStrategyDialog.vue'
import TransformationRuleDialog from './components/TransformationRuleDialog.vue'
import FormatFileConfigDialog from './components/FormatFileConfigDialog.vue'
import DataSourceAccessRuleDialog from './components/DataSourceAccessRuleDialog.vue'
import AccessAnalysisMonitorDialog from './components/AccessAnalysisMonitorDialog.vue'
import DataSourceHealthMonitorDialog from './components/DataSourceHealthMonitorDialog.vue'
import SourceAnalysisDialog from './components/SourceAnalysisDialog.vue'
import ArchiveManagementDialog from './components/ArchiveManagementDialog.vue'
import DataSourcePermissionVerificationDialog from './components/DataSourcePermissionVerificationDialog.vue'
import DataSourceCapacityConfigDialog from './components/DataSourceCapacityConfigDialog.vue'
import DataSourceDecompressionRuleDialog from './components/DataSourceDecompressionRuleDialog.vue'
import DataCleaningAndMigrationDialog from './components/DataCleaningAndMigrationDialog.vue'
import DataCleaningTaskDialog from './components/DataCleaningTaskDialog.vue'
import RuleConfigDialog from './components/RuleConfigDialog.vue'
import ComplianceCheckDialog from './components/ComplianceCheckDialog.vue'
import DataSourceBloodlineAnalysisDialog from './components/DataSourceBloodlineAnalysisDialog.vue'
import VersionHistoryDialog from './components/VersionHistoryDialog.vue'
import DataSetTab from './components/DataSetTab.vue'
import UserFeedbackTab from '../userFeedbackManagement/components/UserFeedbackTab.vue'
import InterfaceTypeTab from './components/InterfaceTypeTab.vue'
import DataAccess from './dataAccess.vue'

// 路由实例
const router = useRouter()

// 数据源类型定义
interface DataSource {
  id: string
  name: string
  description: string
  version: string
  sourceType: string
  status: boolean
  createTime: string
  createUser: string
  dataSourceCategory?: string // 数据源分类：api, file, stream
  dataSourceTypes?: string[] // 具体的数据源类型
  selectedDatabase?: string // 选择的数据库
  databaseConfigs?: {
    [databaseType: string]: {
      connectionUrl: string
      port: string
      username: string
      password: string
      databaseName: string
      authType: string
    }
  }
}

// 数据源同步配置类型定义
interface DataSyncConfig {
  id: string
  name: string
  sourceDataSource: string
  targetDataSource: string
  syncType: string
  syncFrequency: string
  status: boolean
  createTime: string
  updateTime: string
}

// 缓存键
const STORAGE_KEY = 'dataSourceManagement_data'
const LOG_STORAGE_KEY = 'dataSourceManagement_logs'
const DATA_SYNC_STORAGE_KEY = 'dataSyncConfigs'

// 数据源同步管理相关状态
const showDataSyncDialog = ref(false) // 数据源同步管理弹窗
const showDataSyncFormDialog = ref(false) // 数据源同步配置弹窗
const dataSyncLoading = ref(false) // 同步配置加载状态
const dataSyncList = ref<DataSyncConfig[]>([]) // 同步配置列表
const filteredDataSyncList = ref<DataSyncConfig[]>([]) // 过滤后的同步配置列表
const currentDataSyncRow = ref<DataSyncConfig | null>(null) // 当前编辑的同步配置

// 数据源审计相关状态
const showDataSourceAuditDialog = ref(false) // 数据源审计弹窗

// 数据源权限管理相关状态
const showDataSourcePermissionManagementDialog = ref(false) // 数据源权限管理弹窗
const showAddPermissionDialog = ref(false) // 添加授权弹窗
const permissionVerificationLoading = ref(false) // 权限校验加载状态
const permissionVerificationText = ref('') // 权限校验提示文本

// 权限管理数据
const permissionManagementList = ref<any[]>([]) // 权限管理列表
const filteredPermissionManagementList = ref<any[]>([]) // 过滤后的权限管理列表
const PERMISSION_MANAGEMENT_STORAGE_KEY = 'dataSourcePermissionManagement'

// 权限管理搜索表单
const permissionSearchForm = ref({
  dataSourceName: '',
  roleOrPerson: ''
})

// 当前编辑的权限ID
const currentEditPermissionId = ref('')

// 权限管理数据结构
interface PermissionManagement {
  id: string
  sequence: number
  dataSourceName: string
  authMode: string // 授权模式：指定人员、指定角色
  roleOrPerson: string // 角色/人员
  authPermissions: string[] // 授权权限
  authTime: string // 授权时间
}

// 添加授权表单数据
const addPermissionForm = ref({
  dataSource: '', // 数据源
  authMode: '指定人员', // 授权模式
  authContent: '', // 授权内容
  selectedDepartments: [], // 选中的部门
  selectedPersons: [], // 选中的人员
  selectedRoles: [], // 选中的角色
  permissions: {
    view: false, // 查看
    add: false, // 新增
    edit: false, // 编辑
    delete: false // 删除
  }
})

// 部门和人员数据
const departmentData = ref([
  {
    id: 1,
    name: '信息技术部',
    expanded: false,
    persons: ['张伟', '李明', '王芳', '陈静']
  },
  {
    id: 2,
    name: '数据管理部',
    expanded: false,
    persons: ['刘强', '赵敏', '孙涛']
  },
  {
    id: 3,
    name: '业务运营部',
    expanded: false,
    persons: ['周杰', '吴娜', '郑磊', '黄丽', '林峰']
  },
  {
    id: 4,
    name: '质量监控部',
    expanded: false,
    persons: ['马超', '朱琳']
  }
])

// 角色数据
const roleData2 = ref([
  { id: 1, name: '数据管理岗' },
  { id: 2, name: '分管领导' },
  { id: 3, name: '科室负责人' },
  { id: 4, name: '工作人员' }
])

// 数据源访问审计搜索表单
const accessAuditSearchForm = ref({
  dataSource: '',
  operationType: '',
  operatorUser: '',
  startTime: '',
  endTime: ''
})

// 当前激活的Tab
const activeTab = ref('dataSource')

// 加载状态
const loading = ref(false)

// 表格高度
const tableHeight = ref(400)

// 搜索表单
const searchFormProp = ref([
  { label: '数据源版本', prop: 'version', type: 'text' },
  { label: '数据源名称', prop: 'name', type: 'text' },
  { label: '数据源类型', prop: 'sourceType', type: 'text' }
])
const searchForm = ref({ version: '', name: '', sourceType: '' })

// 操作按钮
const buttons = [
  { label: '详情', type: 'info', code: 'detail' },
  { label: '修改', type: 'primary', code: 'edit' },
  { label: '删除', type: 'danger', code: 'delete', popconfirm: '确认删除吗?' },
  // 更多操作选项
  { label: '数据源权限验证', code: 'permission', more: true },
  { label: '数据源容量配置', code: 'config', more: true },
  { label: '数据源解压配置', code: 'parse', more: true },
  { label: '数据源清理与迁移配置', code: 'process', more: true },
  { label: '清洗任务', code: 'clean', more: true },
  { label: '规则配置', code: 'rules', more: true },
  { label: '访问权限调整', code: 'accessPermission', more: true },
  { label: '合规性检查', code: 'compliance', more: true },
]

// 数据源同步管理搜索表单
const dataSyncSearchForm = ref({
  name: '',
  sourceDataSource: '',
  status: ''
})

const dataSyncSearchFormProp = ref([
  {label: '任务名称', prop: 'name', type: 'text'},
  {label: '数据源', prop: 'sourceDataSource', type: 'select', options: []},
  {label: '状态', prop: 'status', type: 'select', options: [
    {label: '全部', value: ''},
    {label: '启用', value: 'true'},
    {label: '禁用', value: 'false'}
  ]}
])

// 数据源同步配置表单
const dataSyncFormRef = ref()
const dataSyncForm = ref<Partial<DataSyncConfig>>({
  name: '',
  sourceDataSource: '',
  targetDataSource: '',
  syncType: '实时同步',
  syncFrequency: '每小时',
  status: true
})

const dataSyncFormProps = ref([
  {label: '任务名称', prop: 'name', type: 'text'},
  {label: '源数据源', prop: 'sourceDataSource', type: 'select', options: []},
  {label: '目标数据源', prop: 'targetDataSource', type: 'select', options: []},
  {label: '同步类型', prop: 'syncType', type: 'select', options: [
    {label: '实时同步', value: '实时同步'},
    {label: '定时同步', value: '定时同步'}
  ]},
  {label: '同步频率', prop: 'syncFrequency', type: 'select', options: [
    {label: '每小时', value: '每小时'},
    {label: '每天', value: '每天'},
    {label: '每周', value: '每周'}
  ]},
  {label: '状态', prop: 'status', type: 'switch'}
])

const dataSyncFormRules = {
  name: [{required: true, message: '请输入任务名称', trigger: 'blur'}],
  sourceDataSource: [{required: true, message: '请选择数据源', trigger: 'change'}],
  targetDataSource: [{required: true, message: '请选择目标数据源', trigger: 'change'}],
  syncType: [{required: true, message: '请选择同步类型', trigger: 'change'}],
  syncFrequency: [{required: true, message: '请选择同步频率', trigger: 'change'}]
}

// 表头配置
const columns = [
  { prop: 'name', label: '数据源名称' },
  { prop: 'description', label: '数据源描述' },
  { prop: 'version', label: '数据源版本' },
  { prop: 'sourceType', label: '数据源类型' },
  { prop: 'status', label: '状态', type: 'switch' },
  { prop: 'createTime', label: '创建时间' },
  { prop: 'createUser', label: '创建人' }
]

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 弹窗相关
const showDialogForm = ref(false)
const dialogFormRef = ref()
const dialogMode = ref<'add' | 'edit' | 'detail'>('add')
const currentRow = ref<DataSource | null>(null)

// 弹窗表单数据
const dialogForm = ref<Partial<DataSource>>({})



// 数据源类型选项
const dataSourceTypes = ref({
  api: {
    label: 'API数据源类型',
    options: [
      { label: 'RESTful API', value: 'restful' },
      { label: 'SOAP API', value: 'soap' },
      { label: 'WebSocket API', value: 'websocket' }
    ]
  },
  file: {
    label: '文件数据源类型',
    options: [
      { label: 'CSV', value: 'csv' },
      { label: 'Excel', value: 'excel' },
      { label: 'JSON', value: 'json' },
      { label: 'XML', value: 'xml' },
      { label: 'TXT', value: 'txt' }
    ]
  },
  stream: {
    label: '数据流数据源类型',
    options: [
      { label: 'Apache Kafka', value: 'kafka' },
      { label: 'RabbitMQ', value: 'rabbitmq' },
      { label: 'Apache Flink', value: 'flink' },
      { label: 'Amazon Kinesis', value: 'kinesis' }
    ]
  }
})

// 数据库选项（简单的名称列表）
const databaseOptions = ref([
  'MySQL', 'Oracle', 'SQL Server', '达梦', 'Hive', 'MongoDB', 'Huawei GaussDB'
])

// 选中的数据源类型和数据库
const selectedDataSourceCategory = ref('') // 主分类：api, file, stream
const selectedDataSourceTypes = ref<string[]>([]) // 具体类型
const selectedDatabase = ref('')

// 数据库配置弹窗状态
const showDatabaseConfigDialog = ref(false)
const selectedDatabaseType = ref('')
const databaseConfigForm = ref({
  connectionUrl: '',
  port: '',
  username: '',
  password: '',
  databaseName: '',
  authType: '' // 身份认证类型
})

// 弹窗表单属性（只包含基础信息）
const dialogFormProps = computed(() => {
  const isDetail = dialogMode.value === 'detail'
  return [
    { label: '数据源名称', prop: 'name', type: 'text', disabled: isDetail },
    { label: '数据源描述', prop: 'description', type: 'textarea', disabled: isDetail },
    { label: '数据源版本', prop: 'version', type: 'text', disabled: isDetail }
  ]
})

// 数据库配置表单属性（动态生成）
const getDatabaseConfigFormProps = () => {
  const isDetailMode = dialogMode.value === 'detail'

  const baseProps = [
    { label: '连接地址', prop: 'connectionUrl', type: 'text', disabled: isDetailMode },
    { label: '端口', prop: 'port', type: 'text', disabled: isDetailMode },
    { label: '数据库名', prop: 'databaseName', type: 'text', disabled: isDetailMode }
  ]

  // SQL Server 和达梦需要身份认证字段
  if (selectedDatabaseType.value === 'SQL Server' || selectedDatabaseType.value === '达梦') {
    baseProps.push({
      label: '身份认证',
      prop: 'authType',
      type: 'select',
      disabled: isDetailMode,
      options: [
        { label: 'SQL Server', value: 'sqlserver' },
        { label: 'Windows', value: 'windows' }
      ]
    })
  }

  baseProps.push(
    { label: '用户名', prop: 'username', type: 'text', disabled: isDetailMode },
    { label: '密码', prop: 'password', type: 'password', disabled: isDetailMode }
  )

  return baseProps
}

// 数据库配置表单验证规则（动态生成）
const getDatabaseConfigFormRules = () => {
  const baseRules = {
    connectionUrl: [{ required: true, message: '请输入连接地址', trigger: 'blur' }],
    port: [
      { required: true, message: '请输入端口', trigger: 'blur' },
      { pattern: /^\d+$/, message: '端口必须为数字', trigger: 'blur' }
    ],
    databaseName: [{ required: true, message: '请输入数据库名', trigger: 'blur' }],
    username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
    password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
  }

  // SQL Server 和达梦需要身份认证验证
  if (selectedDatabaseType.value === 'SQL Server' || selectedDatabaseType.value === '达梦') {
    baseRules.authType = [{ required: true, message: '请选择身份认证方式', trigger: 'change' }]
  }

  return baseRules
}

// 表单验证规则
const dialogFormRules = {
  name: [
    { required: true, message: '请输入数据源名称', trigger: 'blur' },
    { min: 2, max: 50, message: '数据源名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入数据源描述', trigger: 'blur' },
    { min: 5, max: 200, message: '数据源描述长度在 5 到 200 个字符', trigger: 'blur' }
  ],
  version: [
    { required: true, message: '请输入数据源版本', trigger: 'blur' },
    { pattern: /^V\d+\.\d+$/, message: '版本格式应为 V1.0 格式', trigger: 'blur' }
  ]
}

// 数据存储
let dataSourceList = ref<DataSource[]>([])

// 日志数据类型定义
interface DataSourceLog {
  id: string
  sequence: number
  operatorUser: string
  operationType: string
  operationDataSource: string
  operationTime: string
  // 访问审计扩展字段
  accessTime?: string
  accessUser?: string
  ipAddress?: string
}

// 日志相关状态
const showLogDialog = ref(false)
const logList = ref<DataSourceLog[]>([])
const logSearchForm = ref({
  dataSource: '',
  operationTime: ''
})

// 数据备份与恢复规则弹窗
const showBackupRuleDialog = ref(false)
const backupRuleForm = ref({
  backupStartTime: '',
  fullBackupFrequency: '每小时',
  incrementalBackupFrequency: '每小时',
  dataCleanupPolicy: '保留30天',
  recoveryToTime: '',
  recoveryExecutionTime: ''
})

// 数据备份与恢复验证弹窗
const showBackupVerifyDialog = ref(false)
const backupVerifyForm = ref({
  type: '数据备份验证',
  dataSource: '',
  recoveryPoint: ''
})
const verifyResult = ref('')

// 访问权限调整弹窗
const showAccessPermissionDialog = ref(false)
const accessPermissionForm = ref({
  objectType: '用户',
  objectSelection: '',
  readPermission: false,
  tablePermission: false,
  fieldPermission: false,
  viewPermission: false,
  editPermission: false,
  addPermission: false,
  deletePermission: false
})

// 用户和角色数据
const userData = ref([
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' }
])

const roleData = ref([
  { id: 1, name: '系统管理员' },
  { id: 2, name: '普通用户' }
])

// 权限审计数据存储
const permissionAuditData = ref([])

// 数据源权限审计弹窗
const showPermissionAuditDialog = ref(false)

// 数据源访问审计弹窗
const showAccessAuditDialog = ref(false)

// 模拟数据
const generateMockData = (): DataSource[] => {
  return [
    {
      id: '1',
      name: 'MySQL客户数据库',
      description: '核心客户信息管理数据库，存储用户基础信息、订单数据、交易记录等关键业务数据',
      version: '8.0.35',
      sourceType: 'MySQL',
      status: true,
      createTime: '2024-01-15 09:30:00',
      createUser: '张三',
      selectedDatabase: 'MySQL',
      databaseConfigs: {
        MySQL: {
          connectionUrl: '*************',
          port: '3306',
          username: 'customer_admin',
          password: '******',
          databaseName: 'customer_db',
          authType: '用户名密码'
        },
        Oracle: {
          connectionUrl: '*************',
          port: '1521',
          username: 'oracle_user',
          password: '******',
          databaseName: 'oracle_backup',
          authType: 'Kerberos'
        },
        'SQL Server': {
          connectionUrl: '*************',
          port: '1433',
          username: 'sqlserver_user',
          password: '******',
          databaseName: 'sqlserver_backup',
          authType: 'SQL Server'
        }
      }
    },
    {
      id: '2',
      name: 'Oracle财务数据仓库',
      description: '企业级财务数据仓库，集成财务报表、成本核算、预算管理等财务核心数据',
      version: '19c Enterprise',
      sourceType: 'Oracle',
      status: true,
      createTime: '2024-02-20 14:15:00',
      createUser: '李四',
      selectedDatabase: 'Oracle',
      databaseConfigs: {
        Oracle: {
          connectionUrl: '*************',
          port: '1521',
          username: 'finance_admin',
          password: '******',
          databaseName: 'finance_warehouse',
          authType: 'Kerberos'
        },
        MySQL: {
          connectionUrl: '*************',
          port: '3306',
          username: 'mysql_backup',
          password: '******',
          databaseName: 'finance_backup',
          authType: '用户名密码'
        }
      }
    },
    {
      id: '3',
      name: 'SQL Server业务分析库',
      description: '业务智能分析数据库，支持实时报表、数据挖掘、商业智能分析等高级分析功能',
      version: '2022 Enterprise',
      sourceType: 'SQL Server',
      status: false,
      createTime: '2024-03-10 16:20:00',
      createUser: '王五',
      selectedDatabase: 'SQL Server',
      databaseConfigs: {
        'SQL Server': {
          connectionUrl: '192.168.1.300',
          port: '1433',
          username: 'bi_analyst',
          password: '******',
          databaseName: 'business_intelligence',
          authType: 'SQL Server'
        }
      }
    },
    {
      id: '4',
      name: '达梦政务数据库',
      description: '政务系统专用数据库，符合国产化要求，存储政务办公、公共服务等敏感数据',
      version: '*********',
      sourceType: '达梦',
      status: true,
      createTime: '2024-04-15 10:45:00',
      createUser: '赵六',
      selectedDatabase: '达梦',
      databaseConfigs: {
        '达梦': {
          connectionUrl: '192.168.1.400',
          port: '5236',
          username: 'gov_admin',
          password: '******',
          databaseName: 'government_db',
          authType: 'Windows'
        },
        MongoDB: {
          connectionUrl: '192.168.1.401',
          port: '27017',
          username: 'mongo_user',
          password: '******',
          databaseName: 'document_store',
          authType: 'SCRAM-SHA-1'
        }
      }
    },
    {
      id: '5',
      name: '外部API数据源',
      description: '第三方API接口数据源，实时获取外部合作伙伴的数据，包括天气、地理位置、支付等服务',
      version: 'v2.1.0',
      sourceType: 'API',
      status: true,
      createTime: '2024-05-20 13:30:00',
      createUser: '孙七',
      selectedDatabase: '',
      databaseConfigs: {}
    },
    {
      id: '6',
      name: 'MongoDB文档数据库',
      description: '非关系型文档数据库，存储用户行为日志、产品评论、社交媒体数据等非结构化数据',
      version: '7.0.2',
      sourceType: 'MongoDB',
      status: true,
      createTime: '2024-06-01 11:15:00',
      createUser: '周八',
      selectedDatabase: 'MongoDB',
      databaseConfigs: {
        MongoDB: {
          connectionUrl: '192.168.1.500',
          port: '27017',
          username: 'mongo_admin',
          password: '******',
          databaseName: 'user_behavior',
          authType: 'SCRAM-SHA-256'
        }
      }
    },
    {
      id: '7',
      name: 'Redis缓存数据库',
      description: '高性能内存数据库，用于缓存热点数据、会话管理、实时计数器等高频访问场景',
      version: '7.2.0',
      sourceType: 'Redis',
      status: true,
      createTime: '2024-06-15 15:45:00',
      createUser: '吴九',
      selectedDatabase: 'Redis',
      databaseConfigs: {
        Redis: {
          connectionUrl: '192.168.1.600',
          port: '6379',
          username: 'redis_user',
          password: '******',
          databaseName: '0',
          authType: 'Password'
        }
      }
    },
    {
      id: '8',
      name: 'Elasticsearch搜索引擎',
      description: '分布式搜索和分析引擎，支持全文检索、日志分析、实时数据分析等复杂查询需求',
      version: '8.9.1',
      sourceType: 'Elasticsearch',
      status: false,
      createTime: '2024-07-01 09:20:00',
      createUser: '郑十',
      selectedDatabase: 'Elasticsearch',
      databaseConfigs: {
        Elasticsearch: {
          connectionUrl: '192.168.1.700',
          port: '9200',
          username: 'elastic',
          password: '******',
          databaseName: 'search_index',
          authType: 'Basic'
        }
      }
    }
  ]
}

// 从缓存加载数据
const loadFromCache = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      dataSourceList.value = JSON.parse(cached)
    } else {
      // 如果没有缓存数据，生成模拟数据
      dataSourceList.value = generateMockData()
      saveToCache() // 保存到缓存
    }

    // 数据源加载后，重新生成基于实际数据源的日志数据
    regenerateLogsBasedOnDataSources()
  } catch (error) {
    console.error('加载数据失败:', error)
    // 如果加载失败，生成模拟数据
    dataSourceList.value = generateMockData()
    saveToCache() // 保存到缓存
    regenerateLogsBasedOnDataSources()
  }
}

// 基于实际数据源重新生成日志数据
const regenerateLogsBasedOnDataSources = () => {
  // 只有在日志为空或者数据源名称不匹配时才重新生成
  const currentDataSourceNames = dataSourceList.value.map(ds => ds.name)
  const logDataSourceNames = logList.value.map(log => log.operationDataSource)

  const hasMatchingDataSources = logDataSourceNames.some(name =>
    currentDataSourceNames.includes(name)
  )

  if (logList.value.length === 0 || !hasMatchingDataSources) {
    logList.value = generateMockLogs()
    saveLogsToCache()
  }
}

// 保存到缓存
const saveToCache = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(dataSourceList.value))
    // 同步识别数据
    syncIdentificationWithDataSources()
  } catch (error) {
    console.error('保存数据失败:', error)
  }
}

// 加载日志数据
const loadLogsFromCache = () => {
  try {
    const cached = localStorage.getItem(LOG_STORAGE_KEY)
    if (cached) {
      logList.value = JSON.parse(cached)
    } else {
      logList.value = generateMockLogs()
      saveLogsToCache()
    }
  } catch (error) {
    console.error('加载日志数据失败:', error)
    logList.value = generateMockLogs()
  }
}

// 保存日志到缓存
const saveLogsToCache = () => {
  try {
    localStorage.setItem(LOG_STORAGE_KEY, JSON.stringify(logList.value))
  } catch (error) {
    console.error('保存日志数据失败:', error)
  }
}

// 生成模拟日志数据
const generateMockLogs = (): DataSourceLog[] => {
  // 基于实际数据源列表生成日志数据
  const actualDataSources = dataSourceList.value.length > 0
    ? dataSourceList.value.map(ds => ds.name)
    : ['MySQL数据库1', 'Oracle数据库1', '达梦数据库1', 'SQL Server数据库1']

  const operationTypes = ['新增', '查看', '编辑', '删除']
  const users = ['当前用户', '张安', '李四', '王五']

  const logs: DataSourceLog[] = []

  // 为每个实际数据源生成1-2条日志记录
  actualDataSources.forEach((dataSourceName, index) => {
    // 第一条记录
    logs.push({
      id: `${index * 2 + 1}`,
      sequence: index * 2 + 1,
      operatorUser: users[index % users.length],
      operationType: operationTypes[index % operationTypes.length],
      operationDataSource: dataSourceName,
      operationTime: `2025.7.${21 - index} 0${index}:${17 + index}:${19 + index}`
    })

    // 第二条记录（如果有足够的数据源）
    if (index < 3) {
      logs.push({
        id: `${index * 2 + 2}`,
        sequence: index * 2 + 2,
        operatorUser: users[(index + 1) % users.length],
        operationType: operationTypes[(index + 1) % operationTypes.length],
        operationDataSource: dataSourceName,
        operationTime: `2025.7.${20 - index} 0${index + 1}:${27 + index}:${27 + index}`
      })
    }
  })

  return logs
}

// 添加操作日志
const addOperationLog = (operationType: string, dataSourceName: string) => {
  const newLog: DataSourceLog = {
    id: Date.now().toString(),
    sequence: logList.value.length + 1,
    operatorUser: '当前用户',
    operationType,
    operationDataSource: dataSourceName,
    operationTime: new Date().toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'numeric',
      day: 'numeric'
    }).replace(/\//g, '.')
  }

  logList.value.unshift(newLog) // 新日志添加到顶部
  saveLogsToCache()
}

// 获取过滤后的数据
const getFilteredData = () => {
  let filtered = dataSourceList.value

  if (searchForm.value.name) {
    filtered = filtered.filter(item =>
      item.name.toLowerCase().includes(searchForm.value.name.toLowerCase())
    )
  }
  if (searchForm.value.version) {
    filtered = filtered.filter(item =>
      item.version.toLowerCase().includes(searchForm.value.version.toLowerCase())
    )
  }
  if (searchForm.value.sourceType) {
    filtered = filtered.filter(item =>
      item.sourceType.toLowerCase().includes(searchForm.value.sourceType.toLowerCase())
    )
  }

  pagination.total = filtered.length
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  return filtered.slice(start, end)
}

// Tab切换
const handleTabClick = (tab: any) => {
  // Element Plus的tab对象可能有不同的属性名
  const tabName = tab.name || tab.paneName || tab.props?.name
  activeTab.value = tabName
  console.log('Tab切换到:', tabName, 'tab对象:', tab)
}

// 查询
const onSearch = () => {
  pagination.page = 1
}

// 重置搜索
const onReset = () => {
  searchForm.value = { version: '', name: '', sourceType: '' }
  pagination.page = 1
}



// 数据源类型分类选择处理
const onDataSourceCategoryChange = (category: string) => {
  selectedDataSourceCategory.value = category
  selectedDataSourceTypes.value = []
}

// 数据库选择处理
// 检查某个数据库是否有配置
const hasConfigForDatabase = (database: string) => {
  if (!currentRow.value?.databaseConfigs) {
    return false
  }
  return !!currentRow.value.databaseConfigs[database]
}

const onDatabaseSelect = (database: string) => {
  selectedDatabase.value = database
  selectedDatabaseType.value = database

  // 根据模式决定是否回显数据库配置
  if ((dialogMode.value === 'edit' || dialogMode.value === 'detail') && currentRow.value?.databaseConfigs) {
    // 获取当前选择的数据库类型的配置
    const currentDatabaseConfig = currentRow.value.databaseConfigs[database]

    if (currentDatabaseConfig) {
      // 编辑/详情模式：回显已保存的数据库配置
      databaseConfigForm.value = { ...currentDatabaseConfig }
    } else if (dialogMode.value === 'detail') {
      // 详情模式但没有该数据库的配置数据，显示提示信息
      databaseConfigForm.value = {
        connectionUrl: '暂无配置',
        port: '暂无配置',
        username: '暂无配置',
        password: '暂无配置',
        databaseName: '暂无配置',
        authType: '暂无配置'
      }
    } else {
      // 编辑模式但没有该数据库的配置，重置表单
      databaseConfigForm.value = {
        connectionUrl: '',
        port: '',
        username: '',
        password: '',
        databaseName: '',
        authType: ''
      }
    }
  } else if (dialogMode.value === 'detail') {
    // 详情模式但没有配置数据，显示提示信息
    databaseConfigForm.value = {
      connectionUrl: '暂无配置',
      port: '暂无配置',
      username: '暂无配置',
      password: '暂无配置',
      databaseName: '暂无配置',
      authType: '暂无配置'
    }
  } else {
    // 新增模式：重置配置表单
    databaseConfigForm.value = {
      connectionUrl: '',
      port: '',
      username: '',
      password: '',
      databaseName: '',
      authType: ''
    }
  }

  // 弹出数据库配置弹窗
  showDatabaseConfigDialog.value = true
  console.log('选择了数据库:', database)
}

// 数据库配置确认
const onDatabaseConfigConfirm = () => {
  // 保存数据库配置信息到当前数据源
  if (!currentRow.value) {
    currentRow.value = {}
  }

  // 确保 databaseConfigs 对象存在
  if (!currentRow.value.databaseConfigs) {
    currentRow.value.databaseConfigs = {}
  }

  // 保存当前数据库类型的配置
  currentRow.value.databaseConfigs[selectedDatabaseType.value] = { ...databaseConfigForm.value }

  console.log('数据库配置保存:', {
    database: selectedDatabaseType.value,
    config: databaseConfigForm.value
  })

  // 关闭配置弹窗
  showDatabaseConfigDialog.value = false

  ElMessage.success('数据库配置保存成功')
}

// 新增按钮
const onClickAdd = async () => {
  // 新增操作需要权限校验
  const hasPermission = await checkPermission('新增', '数据源')
  if (hasPermission) {
    dialogMode.value = 'add'
    currentRow.value = null
    dialogForm.value = {}
    selectedDataSourceCategory.value = ''
    selectedDataSourceTypes.value = []
    selectedDatabase.value = ''
    showDialogForm.value = true
  }
}

// 数据源同步管理
const onClickDataSyncManagement = () => {
  loadDataSyncConfigs()
  updateDataSyncFormOptions()
  showDataSyncDialog.value = true
}



// 数据源审计
const onClickDataSourceAudit = () => {
  showDataSourceAuditDialog.value = true
}

// 数据源权限管理
const onClickDataSourcePermissionManagement = () => {
  loadPermissionManagementFromCache()
  showDataSourcePermissionManagementDialog.value = true
}

// 加载权限管理数据
const loadPermissionManagementFromCache = () => {
  try {
    const cached = localStorage.getItem(PERMISSION_MANAGEMENT_STORAGE_KEY)
    if (cached) {
      permissionManagementList.value = JSON.parse(cached)
    } else {
      permissionManagementList.value = generateMockPermissionData()
      savePermissionManagementToCache()
    }
    // 初始化过滤列表
    filteredPermissionManagementList.value = [...permissionManagementList.value]
  } catch (error) {
    console.error('加载权限管理数据失败:', error)
    permissionManagementList.value = generateMockPermissionData()
    filteredPermissionManagementList.value = [...permissionManagementList.value]
  }
}

// 保存权限管理数据
const savePermissionManagementToCache = () => {
  try {
    localStorage.setItem(PERMISSION_MANAGEMENT_STORAGE_KEY, JSON.stringify(permissionManagementList.value))
  } catch (error) {
    console.error('保存权限管理数据失败:', error)
  }
}

// 生成模拟权限数据
const generateMockPermissionData = (): PermissionManagement[] => {
  return [
    {
      id: '1',
      sequence: 1,
      dataSourceName: '数据源1',
      authMode: '指定人员',
      roleOrPerson: '张三、李四',
      authPermissions: ['新增', '编辑', '删除', '查看'],
      authTime: '2025.7.29'
    },
    {
      id: '2',
      sequence: 2,
      dataSourceName: '数据源2',
      authMode: '指定角色',
      roleOrPerson: '角色2',
      authPermissions: ['新增', '编辑', '删除', '查看'],
      authTime: '手机号格式'
    },
    {
      id: '3',
      sequence: 3,
      dataSourceName: '数据源3',
      authMode: '指定角色',
      roleOrPerson: '角色3',
      authPermissions: ['新增', '编辑', '删除', '查看'],
      authTime: '手机号格式'
    },
    {
      id: '4',
      sequence: 4,
      dataSourceName: '数据源4',
      authMode: '指定角色',
      roleOrPerson: '角色4',
      authPermissions: ['新增', '编辑', '删除', '查看'],
      authTime: '手机号格式'
    }
  ]
}

// 添加授权确认
const onAddPermissionConfirm = () => {
  // 验证表单
  if (!addPermissionForm.value.dataSource) {
    ElMessage.error('请选择数据源')
    return
  }

  const selectedPermissions = Object.keys(addPermissionForm.value.permissions)
    .filter(key => addPermissionForm.value.permissions[key])
    .map(key => {
      const permissionMap = {
        view: '查看',
        add: '新增',
        edit: '编辑',
        delete: '删除'
      }
      return permissionMap[key]
    })

  if (selectedPermissions.length === 0) {
    ElMessage.error('请至少选择一个操作权限')
    return
  }

  let roleOrPerson = ''
  if (addPermissionForm.value.authMode === '指定人员') {
    if (addPermissionForm.value.selectedPersons.length === 0) {
      ElMessage.error('请选择人员')
      return
    }
    roleOrPerson = addPermissionForm.value.selectedPersons.join('、')
  } else {
    if (addPermissionForm.value.selectedRoles.length === 0) {
      ElMessage.error('请选择角色')
      return
    }
    roleOrPerson = addPermissionForm.value.selectedRoles.join('、')
  }

  if (currentEditPermissionId.value) {
    // 编辑模式
    const index = permissionManagementList.value.findIndex(item => item.id === currentEditPermissionId.value)
    if (index > -1) {
      permissionManagementList.value[index] = {
        ...permissionManagementList.value[index],
        dataSourceName: addPermissionForm.value.dataSource,
        authMode: addPermissionForm.value.authMode,
        roleOrPerson,
        authPermissions: selectedPermissions,
        authTime: new Date().toLocaleDateString('zh-CN').replace(/\//g, '.')
      }
      ElMessage.success('修改授权成功')
    }
  } else {
    // 新增模式
    const newPermission: PermissionManagement = {
      id: Date.now().toString(),
      sequence: permissionManagementList.value.length + 1,
      dataSourceName: addPermissionForm.value.dataSource,
      authMode: addPermissionForm.value.authMode,
      roleOrPerson,
      authPermissions: selectedPermissions,
      authTime: new Date().toLocaleDateString('zh-CN').replace(/\//g, '.')
    }

    permissionManagementList.value.unshift(newPermission)
    ElMessage.success('添加授权成功')
  }

  savePermissionManagementToCache()
  // 更新过滤列表
  filteredPermissionManagementList.value = [...permissionManagementList.value]
  showAddPermissionDialog.value = false

  // 重置表单
  resetAddPermissionForm()
}

// 重置添加授权表单
const resetAddPermissionForm = () => {
  addPermissionForm.value = {
    dataSource: '',
    authMode: '指定人员',
    authContent: '',
    selectedDepartments: [],
    selectedPersons: [],
    selectedRoles: [],
    permissions: {
      view: false,
      add: false,
      edit: false,
      delete: false
    }
  }
  // 重置部门展开状态
  departmentData.value.forEach(dept => {
    dept.expanded = false
  })
  // 清空编辑ID
  currentEditPermissionId.value = ''
}

// 部门点击处理 - 展开/收起部门
const onDepartmentClick = (department) => {
  department.expanded = !department.expanded
}

// 人员点击处理
const onPersonClick = (person) => {
  const index = addPermissionForm.value.selectedPersons.indexOf(person)
  if (index > -1) {
    // 取消选择人员
    addPermissionForm.value.selectedPersons.splice(index, 1)
  } else {
    // 选择人员
    addPermissionForm.value.selectedPersons.push(person)
  }
}

// 角色点击处理
const onRoleClick = (role) => {
  const index = addPermissionForm.value.selectedRoles.indexOf(role.name)
  if (index > -1) {
    addPermissionForm.value.selectedRoles.splice(index, 1)
  } else {
    addPermissionForm.value.selectedRoles.push(role.name)
  }
}

// 权限管理搜索
const onPermissionSearch = () => {
  const searchForm = permissionSearchForm.value

  filteredPermissionManagementList.value = permissionManagementList.value.filter(item => {
    // 数据源名称过滤
    if (searchForm.dataSourceName && !item.dataSourceName.includes(searchForm.dataSourceName)) {
      return false
    }

    // 角色/人员过滤
    if (searchForm.roleOrPerson && !item.roleOrPerson.includes(searchForm.roleOrPerson)) {
      return false
    }

    return true
  })

  console.log('执行权限管理搜索', searchForm, '结果数量:', filteredPermissionManagementList.value.length)
}

// 权限管理重置
const onPermissionReset = () => {
  permissionSearchForm.value = {
    dataSourceName: '',
    roleOrPerson: ''
  }
  // 重置后显示所有数据
  filteredPermissionManagementList.value = [...permissionManagementList.value]
  // ElMessage.success('搜索条件已重置')
}

// 修改权限
const onEditPermission = (row) => {
  // 填充表单数据
  addPermissionForm.value = {
    dataSource: row.dataSourceName,
    authMode: row.authMode,
    authContent: '',
    selectedDepartments: [],
    selectedPersons: row.authMode === '指定人员' ? row.roleOrPerson.split('、') : [],
    selectedRoles: row.authMode === '指定角色' ? row.roleOrPerson.split('、') : [],
    permissions: {
      view: row.authPermissions.includes('查看'),
      add: row.authPermissions.includes('新增'),
      edit: row.authPermissions.includes('编辑'),
      delete: row.authPermissions.includes('删除')
    }
  }

  // 保存当前编辑的权限ID
  currentEditPermissionId.value = row.id
  showAddPermissionDialog.value = true
}

// 删除权限
const onDeletePermission = (row) => {
  ElMessageBox.confirm('确认删除该权限配置吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = permissionManagementList.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      permissionManagementList.value.splice(index, 1)
      savePermissionManagementToCache()
      // 更新过滤列表
      filteredPermissionManagementList.value = [...permissionManagementList.value]
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 权限校验函数
const checkPermission = (operation: string, dataSourceName: string): Promise<boolean> => {
  return new Promise((resolve) => {
    // 显示权限校验动画
    permissionVerificationLoading.value = true
    permissionVerificationText.value = '正在进行身份验证，请稍后'

    // 模拟权限校验过程，2.5秒后返回结果
    setTimeout(() => {
      permissionVerificationLoading.value = false
      permissionVerificationText.value = ''

      // 模拟权限校验结果（90%概率通过）
      const hasPermission = Math.random() > 0.1

      if (hasPermission) {
        // 验证通过，不显示成功提示，直接执行操作
        resolve(true)
      } else {
        ElMessage.error(`您没有对"${dataSourceName}"进行${operation}操作的权限`)
        resolve(false)
      }
    }, 2500)
  })
}

// 带权限校验的操作处理
const handleOperationWithPermission = async (operation: string, row: any, originalHandler: Function) => {
  const hasPermission = await checkPermission(operation, row.name)
  if (hasPermission) {
    originalHandler()
  }
}

// 异常处理相关
const showExceptionDialog = ref(false)
const exceptionList = ref<any[]>([])
const EXCEPTION_STORAGE_KEY = 'dataSourceExceptions'

// 异常处理数据结构
interface DataSourceException {
  id: string
  sequence: number
  dataSourceName: string
  exceptionType: string
  exceptionTime: string
  status: string
  operation: string
}

// 异常处理弹窗
const onClickExceptionHandling = () => {
  loadExceptionsFromCache()
  showExceptionDialog.value = true
}

// 加载异常数据
const loadExceptionsFromCache = () => {
  try {
    const cached = localStorage.getItem(EXCEPTION_STORAGE_KEY)
    if (cached) {
      exceptionList.value = JSON.parse(cached)
    } else {
      exceptionList.value = generateMockExceptions()
      saveExceptionsToCache()
    }
  } catch (error) {
    console.error('加载异常数据失败:', error)
    exceptionList.value = generateMockExceptions()
  }
}

// 保存异常数据到缓存
const saveExceptionsToCache = () => {
  try {
    localStorage.setItem(EXCEPTION_STORAGE_KEY, JSON.stringify(exceptionList.value))
  } catch (error) {
    console.error('保存异常数据失败:', error)
  }
}

// 生成模拟异常数据
const generateMockExceptions = (): DataSourceException[] => {
  const actualDataSources = dataSourceList.value.length > 0
    ? dataSourceList.value.map(ds => ds.name)
    : ['MySQL数据库1', 'Oracle数据库1', '达梦数据库1', 'SQL Server数据库1']

  return actualDataSources.slice(0, 2).map((name, index) => ({
    id: `exc_${Date.now()}_${index}`,
    sequence: index + 1,
    dataSourceName: name,
    exceptionType: '数据库连接失败',
    exceptionTime: new Date(Date.now() - Math.random() * 86400000).toLocaleString('zh-CN'),
    status: '异常',
    operation: '处理'
  }))
}

// 添加异常记录（在新增数据源时调用）
const addExceptionRecord = (dataSourceName: string) => {
  // 30% 概率模拟连接失败
  if (Math.random() < 0.3) {
    const newException: DataSourceException = {
      id: `exc_${Date.now()}`,
      sequence: exceptionList.value.length + 1,
      dataSourceName,
      exceptionType: '数据库连接失败',
      exceptionTime: new Date().toLocaleString('zh-CN'),
      status: '异常',
      operation: '处理'
    }
    exceptionList.value.unshift(newException)
    saveExceptionsToCache()
    ElMessage.warning(`数据源 ${dataSourceName} 连接测试失败，已记录异常信息`)
  }
}

// 质量报告管理相关
const showQualityReportDialog = ref(false)
const qualityReportActiveTab = ref('generation')
const qualityReportList = ref<any[]>([])
const qualityImprovementList = ref<any[]>([])
const showAddQualityReportDialog = ref(false)
const showAddQualityImprovementDialog = ref(false)
const showReportAnalysisDialog = ref(false)
const currentAnalysisReport = ref<any>(null)
const pieChartRef = ref<HTMLDivElement>()
const QUALITY_REPORT_STORAGE_KEY = 'qualityReports'
const QUALITY_IMPROVEMENT_STORAGE_KEY = 'qualityImprovements'

// 集成策略与任务相关
const showIntegrationStrategyDialog = ref(false)

// 转换规则与任务相关
const showTransformationRuleDialog = ref(false)

// 格式文件配置相关
const showFormatFileConfigDialog = ref(false)

// 数据源接入规则相关
const showDataSourceAccessRuleDialog = ref(false)

// 新增功能弹窗状态
const showAccessAnalysisMonitorDialog = ref(false)
const showDataSourceHealthMonitorDialog = ref(false)
const showSourceAnalysisDialog = ref(false)
const showArchiveManagementDialog = ref(false)
const showRuleConfigDialog = ref(false)
const showComplianceCheckDialog = ref(false)
const showDataSourceBloodlineAnalysisDialog = ref(false)
const showVersionHistoryDialog = ref(false)
const versionHistoryDialogRef = ref()



// 新增弹窗状态
const showDataSourcePermissionVerificationDialog = ref(false)
const showDataSourceCapacityConfigDialog = ref(false)
const showDataSourceDecompressionRuleDialog = ref(false)
const showDataCleaningAndMigrationDialog = ref(false)
const showDataCleaningTaskDialog = ref(false)

// 质量报告数据结构
interface QualityReport {
  id: string
  sequence: number
  reportName: string
  dataSource: string
  createTime: string
  status: string
  buttonText: string
}

interface QualityImprovement {
  id: string
  sequence: number
  dataSource: string
  improvementDescription: string
  responsible: string
  status: string
  createTime: string
}

// 质量报告管理弹窗
const onClickQualityReportManagement = () => {
  loadQualityReportsFromCache()
  loadQualityImprovementsFromCache()
  showQualityReportDialog.value = true
}

// 集成策略与任务弹窗
const onClickIntegrationStrategy = () => {
  showIntegrationStrategyDialog.value = true
}

// 转换规则与任务弹窗
const onClickTransformationRule = () => {
  showTransformationRuleDialog.value = true
}

// 格式文件配置弹窗
const onClickFormatFileConfig = () => {
  showFormatFileConfigDialog.value = true
}

// 数据源接入规则弹窗
const onClickDataSourceAccessRule = () => {
  showDataSourceAccessRuleDialog.value = true
}

// 访问与分析监控弹窗
const onClickAccessAnalysisMonitor = () => {
  showAccessAnalysisMonitorDialog.value = true
}

// 数据源健康监控弹窗
const onClickDataSourceHealthMonitor = () => {
  showDataSourceHealthMonitorDialog.value = true
}

// 来源分析弹窗
const onClickSourceAnalysis = () => {
  showSourceAnalysisDialog.value = true
}

// 归档管理弹窗
const onClickArchiveManagement = () => {
  showArchiveManagementDialog.value = true
}

// 规则配置弹窗
const onClickRuleConfig = () => {
  showRuleConfigDialog.value = true
}

// 合规性检查弹窗
const onClickComplianceCheck = () => {
  showComplianceCheckDialog.value = true
}

// 数据源血缘分析弹窗
const onClickDataSourceBloodlineAnalysis = () => {
  showDataSourceBloodlineAnalysisDialog.value = true
}

// 版本历史记录弹窗
const onClickVersionHistory = () => {
  showVersionHistoryDialog.value = true
}

// 生成更新描述
const generateUpdateDescription = (originalData: DataSource, newData: any) => {
  const changes: string[] = []

  // 检查各个字段的变化
  if (originalData.name !== newData.name) {
    changes.push('数据源名称')
  }
  if (originalData.description !== newData.description) {
    changes.push('数据源描述')
  }
  if (originalData.version !== newData.version) {
    changes.push('数据源版本')
  }
  if (originalData.selectedDatabase !== selectedDatabase.value) {
    changes.push('数据库类型')
  }

  // 检查数据库配置的变化
  const currentDbConfig = originalData.databaseConfigs?.[selectedDatabase.value]
  const newDbConfig = databaseConfigForm.value

  if (currentDbConfig && newDbConfig) {
    if (currentDbConfig.connectionUrl !== newDbConfig.connectionUrl) {
      changes.push('连接地址')
    }
    if (currentDbConfig.port !== newDbConfig.port) {
      changes.push('端口')
    }
    if (currentDbConfig.username !== newDbConfig.username) {
      changes.push('用户名')
    }
    if (currentDbConfig.password !== newDbConfig.password) {
      changes.push('密码')
    }
    if (currentDbConfig.databaseName !== newDbConfig.databaseName) {
      changes.push('数据库名称')
    }
    if (currentDbConfig.authType !== newDbConfig.authType) {
      changes.push('认证类型')
    }
  }

  if (changes.length === 0) {
    return '更新了数据源信息'
  }

  return `更新了${changes.join('、')}`
}

// 加载质量报告数据
const loadQualityReportsFromCache = () => {
  try {
    const cached = localStorage.getItem(QUALITY_REPORT_STORAGE_KEY)
    if (cached) {
      qualityReportList.value = JSON.parse(cached)
    } else {
      qualityReportList.value = []
    }
  } catch (error) {
    console.error('加载质量报告数据失败:', error)
    qualityReportList.value = []
  }
}

// 保存质量报告数据
const saveQualityReportsToCache = () => {
  try {
    localStorage.setItem(QUALITY_REPORT_STORAGE_KEY, JSON.stringify(qualityReportList.value))
  } catch (error) {
    console.error('保存质量报告数据失败:', error)
  }
}

// 加载质量改进数据
const loadQualityImprovementsFromCache = () => {
  try {
    const cached = localStorage.getItem(QUALITY_IMPROVEMENT_STORAGE_KEY)
    if (cached) {
      qualityImprovementList.value = JSON.parse(cached)
    } else {
      qualityImprovementList.value = []
    }
  } catch (error) {
    console.error('加载质量改进数据失败:', error)
    qualityImprovementList.value = []
  }
}

// 保存质量改进数据
const saveQualityImprovementsToCache = () => {
  try {
    localStorage.setItem(QUALITY_IMPROVEMENT_STORAGE_KEY, JSON.stringify(qualityImprovementList.value))
  } catch (error) {
    console.error('保存质量改进数据失败:', error)
  }
}

// 新增质量报告
const qualityReportForm = ref<any>({})
const qualityReportFormRef = ref()

const onClickAddQualityReport = () => {
  qualityReportForm.value = {
    reportName: '',
    dataSource: ''
  }
  showAddQualityReportDialog.value = true
}

const onConfirmAddQualityReport = () => {
  qualityReportFormRef.value.validate((valid: boolean) => {
    if (valid) {
      const newReport: QualityReport = {
        id: `qr_${Date.now()}`,
        sequence: qualityReportList.value.length + 1,
        reportName: qualityReportForm.value.reportName,
        dataSource: qualityReportForm.value.dataSource,
        createTime: new Date().toLocaleString('zh-CN'),
        status: '待生成',
        buttonText: '生成报告'
      }
      qualityReportList.value.unshift(newReport)
      saveQualityReportsToCache()
      ElMessage.success('新增质量报告成功')
      showAddQualityReportDialog.value = false
    }
  })
}

// 生成报告/报告分析
const onQualityReportAction = (row: QualityReport) => {
  if (row.buttonText === '生成报告') {
    // 生成报告
    row.buttonText = '报告分析'
    row.status = '已生成'
    saveQualityReportsToCache()
    ElMessage.success('报告生成成功')
  } else {
    // 报告分析
    currentAnalysisReport.value = row
    showReportAnalysisDialog.value = true
    // 延迟初始化饼图，确保DOM已渲染
    nextTick(() => {
      initPieChart()
    })
  }
}

// 新增质量改进
const qualityImprovementForm = ref<any>({})
const qualityImprovementFormRef = ref()

const onClickAddQualityImprovement = () => {
  qualityImprovementForm.value = {
    dataSource: '',
    improvementDescription: '',
    responsible: ''
  }
  showAddQualityImprovementDialog.value = true
}

const onConfirmAddQualityImprovement = () => {
  qualityImprovementFormRef.value.validate((valid: boolean) => {
    if (valid) {
      const newImprovement: QualityImprovement = {
        id: `qi_${Date.now()}`,
        sequence: qualityImprovementList.value.length + 1,
        dataSource: qualityImprovementForm.value.dataSource,
        improvementDescription: qualityImprovementForm.value.improvementDescription,
        responsible: qualityImprovementForm.value.responsible,
        status: '进行中',
        createTime: new Date().toLocaleString('zh-CN')
      }
      qualityImprovementList.value.unshift(newImprovement)
      saveQualityImprovementsToCache()
      ElMessage.success('新增质量改进成功')
      showAddQualityImprovementDialog.value = false
    }
  })
}

// 获取数据源选项
const getDataSourceOptions = () => {
  return dataSourceList.value.map(ds => ({
    label: ds.name,
    value: ds.name
  }))
}

// 责任人选项
const responsibleOptions = [
  { label: '张三', value: '张三' },
  { label: '李四', value: '李四' },
  { label: '王五', value: '王五' }
]

// 初始化饼图
const initPieChart = () => {
  if (!pieChartRef.value) return

  const chart = echarts.init(pieChartRef.value)

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '数据分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 85, name: '正常数据', itemStyle: { color: '#1890ff' } },
          { value: 10, name: '异常数据', itemStyle: { color: '#52c41a' } },
          { value: 5, name: '缺失数据', itemStyle: { color: '#fa8c16' } }
        ]
      }
    ]
  }

  chart.setOption(option)

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 数据源识别相关
const showDataSourceIdentificationDialog = ref(false)
const identificationDetailList = ref<any[]>([])
const identificationStats = ref({
  total: 0,
  identified: 0,
  unidentified: 0,
  sensitive: 0
})
const IDENTIFICATION_STORAGE_KEY = 'dataSourceIdentification'

// 数据源识别数据结构
interface DataSourceIdentification {
  id: string
  sequence: number
  dataSourceName: string
  sensitivity: string
  type: string
  description: string
  identificationTime: string
}

// 数据源识别弹窗
const onClickDataSourceIdentification = () => {
  loadIdentificationFromCache()
  updateIdentificationStats()
  showDataSourceIdentificationDialog.value = true
}

// 加载识别数据
const loadIdentificationFromCache = () => {
  try {
    const cached = localStorage.getItem(IDENTIFICATION_STORAGE_KEY)
    if (cached) {
      identificationDetailList.value = JSON.parse(cached)
    } else {
      identificationDetailList.value = generateIdentificationData()
      saveIdentificationToCache()
    }
  } catch (error) {
    console.error('加载识别数据失败:', error)
    identificationDetailList.value = generateIdentificationData()
  }
}

// 保存识别数据
const saveIdentificationToCache = () => {
  try {
    localStorage.setItem(IDENTIFICATION_STORAGE_KEY, JSON.stringify(identificationDetailList.value))
  } catch (error) {
    console.error('保存识别数据失败:', error)
  }
}

// 生成识别数据
const generateIdentificationData = (): DataSourceIdentification[] => {
  const sensitivityOptions = ['高', '中', '低']
  const typeOptions = ['数据库', 'API数据类型', '文件数据类型', '数据流数据类型']

  return dataSourceList.value.map((ds, index) => ({
    id: `ident_${Date.now()}_${index}`,
    sequence: index + 1,
    dataSourceName: ds.name,
    sensitivity: sensitivityOptions[Math.floor(Math.random() * sensitivityOptions.length)],
    type: typeOptions[Math.floor(Math.random() * typeOptions.length)],
    description: ds.description,
    identificationTime: new Date(Date.now() - Math.random() * 86400000).toLocaleString('zh-CN')
  }))
}

// 更新统计数据
const updateIdentificationStats = () => {
  const total = dataSourceList.value.length
  const identified = Math.floor(total * 0.7) // 70% 已识别
  const sensitive = Math.floor(total * 0.2) // 20% 敏感数据源
  const unidentified = total - identified

  identificationStats.value = {
    total,
    identified,
    unidentified,
    sensitive
  }
}

// 当数据源列表变化时，更新识别数据
const syncIdentificationWithDataSources = () => {
  const existingNames = identificationDetailList.value.map(item => item.dataSourceName)
  const currentNames = dataSourceList.value.map(ds => ds.name)

  // 添加新的数据源到识别列表
  const newDataSources = dataSourceList.value.filter(ds => !existingNames.includes(ds.name))
  if (newDataSources.length > 0) {
    const sensitivityOptions = ['高', '中', '低']
    const typeOptions = ['数据库', 'API数据类型', '文件数据类型', '数据流数据类型']

    newDataSources.forEach((ds, index) => {
      identificationDetailList.value.push({
        id: `ident_${Date.now()}_${index}`,
        sequence: identificationDetailList.value.length + index + 1,
        dataSourceName: ds.name,
        sensitivity: sensitivityOptions[Math.floor(Math.random() * sensitivityOptions.length)],
        type: typeOptions[Math.floor(Math.random() * typeOptions.length)],
        description: ds.description,
        identificationTime: new Date().toLocaleString('zh-CN')
      })
    })
    saveIdentificationToCache()
  }

  // 移除已删除的数据源
  identificationDetailList.value = identificationDetailList.value.filter(item =>
    currentNames.includes(item.dataSourceName)
  )
  saveIdentificationToCache()
  updateIdentificationStats()
}

// 加载数据源同步配置
const loadDataSyncConfigs = () => {
  try {
    const cached = localStorage.getItem(DATA_SYNC_STORAGE_KEY)
    if (cached) {
      dataSyncList.value = JSON.parse(cached)
    } else {
      dataSyncList.value = []
    }
    // 初始化过滤列表，显示所有数据
    filteredDataSyncList.value = [...dataSyncList.value]
  } catch (error) {
    console.error('加载数据源同步配置失败:', error)
    dataSyncList.value = []
    filteredDataSyncList.value = []
  }
}

// 保存数据源同步配置到缓存
const saveDataSyncConfigs = () => {
  try {
    localStorage.setItem(DATA_SYNC_STORAGE_KEY, JSON.stringify(dataSyncList.value))
  } catch (error) {
    console.error('保存数据源同步配置失败:', error)
  }
}

// 更新数据源同步表单选项
const updateDataSyncFormOptions = () => {
  const dataSourceOptions = dataSourceList.value
    .filter(ds => ds.status)
    .map(ds => ({ label: ds.name, value: ds.name }))

  dataSyncSearchFormProp.value[1].options = [
    { label: '全部', value: '' },
    ...dataSourceOptions
  ]
  dataSyncFormProps.value[1].options = dataSourceOptions
  dataSyncFormProps.value[2].options = dataSourceOptions
}

// 数据源同步搜索
const onDataSyncSearch = () => {
  // 基于前端缓存进行搜索过滤
  const searchForm = dataSyncSearchForm.value

  filteredDataSyncList.value = dataSyncList.value.filter(item => {
    // 任务名称过滤
    if (searchForm.name && !item.name.includes(searchForm.name)) {
      return false
    }

    // 源数据源过滤
    if (searchForm.sourceDataSource && item.sourceDataSource !== searchForm.sourceDataSource) {
      return false
    }

    // 状态过滤
    if (searchForm.status && item.status !== searchForm.status) {
      return false
    }

    return true
  })

  console.log('执行搜索', searchForm, '结果数量:', filteredDataSyncList.value.length)
}

// 数据源同步搜索重置
const onDataSyncReset = () => {
  dataSyncSearchForm.value = {
    name: '',
    sourceDataSource: '',
    status: ''
  }
  // 重置后显示所有数据
  filteredDataSyncList.value = [...dataSyncList.value]
  console.log('清空搜索条件')
}

// 数据源同步表格操作
const onDataSyncTableClickButton = ({row, btn}: any) => {
  if (btn.code === 'edit') {
    currentDataSyncRow.value = row
    Object.assign(dataSyncForm.value, row)
    showDataSyncFormDialog.value = true
  } else if (btn.code === 'delete') {
    const index = dataSyncList.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      dataSyncList.value.splice(index, 1)
      saveDataSyncConfigs()
      // 更新过滤列表
      onDataSyncSearch()
      ElMessage.success('删除成功')
    }
  }
}

// 新增数据源同步配置
const onClickAddDataSync = () => {
  currentDataSyncRow.value = null
  dataSyncForm.value = {
    name: '',
    sourceDataSource: '',
    targetDataSource: '',
    syncType: '实时同步',
    syncFrequency: '每小时',
    status: true
  }
  showDataSyncFormDialog.value = true
}

// 数据源同步配置表单提交
const onDataSyncFormConfirm = () => {
  dataSyncFormRef.value.validate((valid: boolean) => {
    if (valid) {
      // 验证源数据源和目标数据源不能相同
      if (dataSyncForm.value.sourceDataSource === dataSyncForm.value.targetDataSource) {
        ElMessage.error('源数据源和目标数据源不能相同')
        return
      }

      dataSyncLoading.value = true

      try {
        const now = new Date().toLocaleString('zh-CN')

        if (currentDataSyncRow.value) {
          // 编辑
          const index = dataSyncList.value.findIndex(item => item.id === currentDataSyncRow.value!.id)
          if (index > -1) {
            dataSyncList.value[index] = {
              ...dataSyncForm.value,
              id: currentDataSyncRow.value.id,
              updateTime: now,
              createTime: dataSyncList.value[index].createTime
            } as DataSyncConfig
          }
          ElMessage.success('编辑成功')
        } else {
          // 新增
          const newConfig: DataSyncConfig = {
            ...dataSyncForm.value,
            id: Date.now().toString(),
            createTime: now,
            updateTime: now
          } as DataSyncConfig

          dataSyncList.value.push(newConfig)
          ElMessage.success('新增成功')
        }

        saveDataSyncConfigs()
        // 更新过滤列表
        onDataSyncSearch()
        showDataSyncFormDialog.value = false
      } catch (error) {
        console.error('保存数据源同步配置失败:', error)
        ElMessage.error('保存失败')
      } finally {
        dataSyncLoading.value = false
      }
    }
  })
}

// 数据源日志管理按钮
const onClickLogManagement = () => {
  loadLogsFromCache()
  showLogDialog.value = true
}

// 数据备份与恢复规则按钮
const onClickBackupRule = () => {
  showBackupRuleDialog.value = true
}

// 数据备份与恢复验证按钮
const onClickBackupVerify = () => {
  backupVerifyForm.value = {
    type: '数据备份验证',
    dataSource: '',
    recoveryPoint: ''
  }
  verifyResult.value = ''
  showBackupVerifyDialog.value = true
}

// 执行验证备份
const executeBackupVerify = () => {
  const dataSourceName = backupVerifyForm.value.dataSource || 'xxx数据源'
  if (backupVerifyForm.value.type === '数据备份验证') {
    verifyResult.value = `${dataSourceName}备份验证成功`
  } else {
    const recoveryDate = backupVerifyForm.value.recoveryPoint ?
      new Date(backupVerifyForm.value.recoveryPoint).toLocaleDateString('zh-CN').replace(/\//g, '.') :
      '2025.7.12'
    verifyResult.value = `${dataSourceName}在${recoveryDate} 验证恢复成功`
  }
}

// 执行验证恢复
const executeRecoveryVerify = () => {
  verifyResult.value = 'xxx数据源在2025.7.12 验证恢复成功'
}

// 访问权限调整按钮
const onClickAccessPermission = () => {
  accessPermissionForm.value = {
    objectType: '用户',
    objectSelection: '',
    readPermission: false,
    tablePermission: false,
    fieldPermission: false,
    viewPermission: false,
    editPermission: false,
    addPermission: false,
    deletePermission: false
  }
  showAccessPermissionDialog.value = true
}

// 根据对象类型获取对象选择数据
const getObjectSelectionData = computed(() => {
  return accessPermissionForm.value.objectType === '用户' ? userData.value : roleData.value
})

// 保存访问权限设置
const saveAccessPermission = () => {
  // 保存权限数据到审计数据中
  const permissionData = {
    id: Date.now(),
    dataSourceName: currentRow.value?.name || '未知数据源',
    objectType: accessPermissionForm.value.objectType,
    objectName: accessPermissionForm.value.objectSelection,
    readPermission: accessPermissionForm.value.readPermission,
    tablePermission: accessPermissionForm.value.tablePermission,
    fieldPermission: accessPermissionForm.value.fieldPermission,
    viewPermission: accessPermissionForm.value.viewPermission,
    editPermission: accessPermissionForm.value.editPermission,
    addPermission: accessPermissionForm.value.addPermission,
    deletePermission: accessPermissionForm.value.deletePermission,
    createTime: new Date().toLocaleString('zh-CN')
  }

  // 添加到权限审计数据
  permissionAuditData.value.push(permissionData)

  // 保存到localStorage
  localStorage.setItem('permissionAuditData', JSON.stringify(permissionAuditData.value))

  ElMessage.success('访问权限调整保存成功')
  showAccessPermissionDialog.value = false
}

// 数据源权限审计
const onClickPermissionAudit = () => {
  // 从localStorage加载权限审计数据
  const savedData = localStorage.getItem('permissionAuditData')
  if (savedData) {
    permissionAuditData.value = JSON.parse(savedData)
  }
  showPermissionAuditDialog.value = true
}

// 数据源访问审计
const onClickAccessAudit = () => {
  // 确保日志数据与数据源管理中的数据一致
  regenerateLogsBasedOnDataSources()
  // 扩展现有日志数据，添加访问审计字段
  extendLogDataForAccessAudit()
  showAccessAuditDialog.value = true
}

// 数据源访问审计搜索
const onAccessAuditSearch = () => {
  // 基于搜索条件过滤数据
  ElMessage.success('搜索完成')
}

// 过滤后的访问审计数据
const filteredAccessAuditData = computed(() => {
  let filtered = [...logList.value]

  if (accessAuditSearchForm.value.dataSource) {
    filtered = filtered.filter(item =>
      item.operationDataSource === accessAuditSearchForm.value.dataSource
    )
  }

  if (accessAuditSearchForm.value.operationType) {
    filtered = filtered.filter(item =>
      item.operationType === accessAuditSearchForm.value.operationType
    )
  }

  if (accessAuditSearchForm.value.operatorUser) {
    filtered = filtered.filter(item =>
      item.operatorUser.includes(accessAuditSearchForm.value.operatorUser)
    )
  }

  if (accessAuditSearchForm.value.startTime) {
    filtered = filtered.filter(item =>
      new Date(item.operationTime) >= new Date(accessAuditSearchForm.value.startTime)
    )
  }

  if (accessAuditSearchForm.value.endTime) {
    filtered = filtered.filter(item =>
      new Date(item.operationTime) <= new Date(accessAuditSearchForm.value.endTime)
    )
  }

  return filtered
})

// 扩展日志数据为访问审计数据
const extendLogDataForAccessAudit = () => {
  logList.value.forEach(log => {
    if (!log.accessTime) {
      // 生成随机访问时间（在操作时间基础上随机偏移）
      const baseTime = new Date(log.operationTime)
      const randomOffset = Math.floor(Math.random() * 3600000) // 随机1小时内
      log.accessTime = new Date(baseTime.getTime() + randomOffset).toLocaleString('zh-CN')

      // 生成访问对象（功能对象/接口对象/数据库对象）
      const accessObjects = [
        '用户管理功能', '数据查询接口', '用户表', '订单表',
        '报表生成功能', 'API接口', '日志表', '配置表',
        '数据导出功能', '统计分析接口', '权限表', '系统表'
      ]
      log.accessUser = accessObjects[Math.floor(Math.random() * accessObjects.length)]

      // 生成随机IP地址
      const ipBase = '192.168.1.'
      const ipSuffix = Math.floor(Math.random() * 254) + 1
      log.ipAddress = ipBase + ipSuffix
    }
  })
}

// 计算权限审计统计数据
const permissionAuditStats = computed(() => {
  const stats = {
    dataSourceCount: dataSourceList.value.length,
    authorizedUserCount: 0,
    permissionItemCount: 0
  }

  if (permissionAuditData.value.length > 0) {
    // 统计授权用户数（去重）
    const uniqueUsers = new Set()
    permissionAuditData.value.forEach(item => {
      if (item.objectType === '用户' && item.objectName) {
        uniqueUsers.add(item.objectName)
      }
    })
    stats.authorizedUserCount = uniqueUsers.size

    // 统计权限项数
    permissionAuditData.value.forEach(item => {
      if (item.viewPermission) stats.permissionItemCount++
      if (item.editPermission) stats.permissionItemCount++
      if (item.addPermission) stats.permissionItemCount++
      if (item.deletePermission) stats.permissionItemCount++
    })
  }

  return stats
})

// 获取过滤后的日志数据
const getFilteredLogs = () => {
  let filtered = logList.value

  if (logSearchForm.value.dataSource) {
    filtered = filtered.filter(item =>
      item.operationDataSource.toLowerCase().includes(logSearchForm.value.dataSource.toLowerCase())
    )
  }
  if (logSearchForm.value.operationTime) {
    filtered = filtered.filter(item =>
      item.operationTime.includes(logSearchForm.value.operationTime)
    )
  }

  return filtered
}

// 日志搜索
const onLogSearch = () => {
  // 搜索逻辑已在 getFilteredLogs 中实现
}

// 日志重置
const onLogReset = () => {
  logSearchForm.value = { dataSource: '', operationTime: '' }
}

// 表格操作点击事件
const onTableClickButton = ({ row, btn }: any) => {
  currentRow.value = row

  if (btn.code === 'detail') {
    // 查看操作需要权限校验
    handleOperationWithPermission('查看', row, () => {
      dialogMode.value = 'detail'
      currentRow.value = row // 保存当前行数据，用于数据库配置回显
      Object.assign(dialogForm.value, row)

    // 回显数据源类型和数据库选择
    // 根据 sourceType 推断数据源类型和数据库选择
    if (row.sourceType) {
      // 设置数据库选择
      selectedDatabase.value = row.sourceType

      // 根据数据库类型推断数据源类别
      const databaseTypes = ['MySQL', 'Oracle', 'SQL Server', '达梦', 'Hive', 'MongoDB', 'Huawei GaussDB']
      if (databaseTypes.includes(row.sourceType)) {
        // 数据库类型，暂时不设置数据源类别，因为模拟数据中没有这个信息
        selectedDataSourceCategory.value = ''
        selectedDataSourceTypes.value = []
      }
    }

    // 如果有保存的数据源类型信息，优先使用
    if (row.dataSourceCategory) {
      selectedDataSourceCategory.value = row.dataSourceCategory
    }
    if (row.dataSourceTypes) {
      selectedDataSourceTypes.value = row.dataSourceTypes
    }
    if (row.selectedDatabase) {
      selectedDatabase.value = row.selectedDatabase
    }

      showDialogForm.value = true
      // 添加查看日志
      addOperationLog('查看', row.name)
    })
  } else if (btn.code === 'edit') {
    // 编辑操作需要权限校验
    handleOperationWithPermission('编辑', row, () => {
      dialogMode.value = 'edit'
      currentRow.value = row // 保存当前行数据，用于数据库配置回显
      Object.assign(dialogForm.value, row)

    // 回显数据源类型和数据库选择
    // 根据 sourceType 推断数据源类型和数据库选择
    if (row.sourceType) {
      // 设置数据库选择
      selectedDatabase.value = row.sourceType

      // 根据数据库类型推断数据源类别
      const databaseTypes = ['MySQL', 'Oracle', 'SQL Server', '达梦', 'Hive', 'MongoDB', 'Huawei GaussDB']
      if (databaseTypes.includes(row.sourceType)) {
        // 数据库类型，暂时不设置数据源类别，因为模拟数据中没有这个信息
        selectedDataSourceCategory.value = ''
        selectedDataSourceTypes.value = []
      }
    }

    // 如果有保存的数据源类型信息，优先使用
    if (row.dataSourceCategory) {
      selectedDataSourceCategory.value = row.dataSourceCategory
    }
    if (row.dataSourceTypes) {
      selectedDataSourceTypes.value = row.dataSourceTypes
    }
    if (row.selectedDatabase) {
      selectedDatabase.value = row.selectedDatabase
    }

      showDialogForm.value = true
    })
  } else if (btn.code === 'delete') {
    // 删除操作需要权限校验
    handleOperationWithPermission('删除', row, () => {
      const index = dataSourceList.value.findIndex(item => item.id === row.id)
      if (index > -1) {
        const dataSourceName = dataSourceList.value[index].name
        dataSourceList.value.splice(index, 1)
        saveToCache()
        // 添加删除日志
        addOperationLog('删除', dataSourceName)
        ElMessage.success('删除成功')
      }
    })
  } else if (btn.code === 'accessPermission') {
    onClickAccessPermission()
  } else if (btn.code === 'permission') {
    showDataSourcePermissionVerificationDialog.value = true
  } else if (btn.code === 'config') {
    showDataSourceCapacityConfigDialog.value = true
  } else if (btn.code === 'parse') {
    showDataSourceDecompressionRuleDialog.value = true
  } else if (btn.code === 'process') {
    showDataCleaningAndMigrationDialog.value = true
  } else if (btn.code === 'clean') {
    showDataCleaningTaskDialog.value = true
  } else if (btn.code === 'rules') {
    showRuleConfigDialog.value = true
  } else if (btn.code === 'compliance') {
    showComplianceCheckDialog.value = true
  } else if (btn.code === 'more') {
    ElMessage.info('更多操作功能')
  }
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
  if (type === 'page') {
    pagination.page = val
  } else {
    pagination.size = val
  }
}

// 块高度变化事件
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 75
}

// 弹窗确认
const onDialogConfirm = () => {
  if (dialogMode.value === 'detail') {
    showDialogForm.value = false
    return
  }

  dialogFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true

      try {
        if (dialogMode.value === 'edit' && currentRow.value) {
          // 编辑
          const index = dataSourceList.value.findIndex(item => item.id === currentRow.value!.id)
          if (index > -1) {
            // 生成更新描述
            const updateDescription = generateUpdateDescription(currentRow.value, dialogForm.value)

            dataSourceList.value[index] = {
              ...dataSourceList.value[index],
              ...dialogForm.value,
              dataSourceCategory: selectedDataSourceCategory.value,
              dataSourceTypes: selectedDataSourceTypes.value,
              selectedDatabase: selectedDatabase.value,
              updateTime: new Date().toISOString().split('T')[0]
            }

            // 添加版本历史记录
            if (versionHistoryDialogRef.value) {
              versionHistoryDialogRef.value.addVersionHistory(updateDescription, '当前用户')
            }

            // 添加编辑日志
            addOperationLog('编辑', dialogForm.value.name!)
          }
          ElMessage.success('编辑成功')
        } else {
          // 新增 - 检查名称是否重复
          const nameExists = dataSourceList.value.some(item => item.name === dialogForm.value.name)
          if (nameExists) {
            ElMessage.error('数据源名称已存在，请重新输入')
            loading.value = false
            return
          }

          const newItem: DataSource = {
            id: Date.now().toString(),
            name: dialogForm.value.name!,
            description: dialogForm.value.description!,
            version: dialogForm.value.version!,
            sourceType: selectedDatabase.value || 'API',
            status: true,
            createTime: new Date().toISOString().split('T')[0],
            createUser: '当前用户',
            dataSourceCategory: selectedDataSourceCategory.value,
            dataSourceTypes: selectedDataSourceTypes.value,
            selectedDatabase: selectedDatabase.value,
            databaseConfigs: currentRow.value?.databaseConfigs || {}
          }
          dataSourceList.value.push(newItem)
          // 添加新增日志
          addOperationLog('新增', dialogForm.value.name!)
          // 模拟连接失败并添加异常记录
          addExceptionRecord(dialogForm.value.name!)
          ElMessage.success('新增成功')
        }

        saveToCache()
        showDialogForm.value = false
      } catch (error) {
        console.error('保存失败:', error)
        ElMessage.error('保存失败')
      } finally {
        loading.value = false
      }
    }
  })
}





// 初始化
onMounted(() => {
  loadFromCache()
  loadLogsFromCache()
  loadDataSyncConfigs()
  // 加载权限审计数据
  const savedPermissionData = localStorage.getItem('permissionAuditData')
  if (savedPermissionData) {
    permissionAuditData.value = JSON.parse(savedPermissionData)
  }
  // 加载异常处理数据
  loadExceptionsFromCache()
  // 加载识别数据
  loadIdentificationFromCache()
  updateIdentificationStats()
  // 加载权限管理数据
  loadPermissionManagementFromCache()
})
</script>

<template>
  <div class="data-source-management">
    <!-- Tab切换 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="main-tabs">
      <el-tab-pane label="数据源" name="dataSource"></el-tab-pane>
      <el-tab-pane label="数据集" name="dataSet"></el-tab-pane>
      <el-tab-pane label="用户反馈" name="userFeedback"></el-tab-pane>
      <el-tab-pane label="接口类型" name="interfaceType"></el-tab-pane>
      <el-tab-pane label="数据接入" name="dataAccess"></el-tab-pane>
    </el-tabs>

    <!-- 数据源Tab内容 -->
    <div v-if="activeTab === 'dataSource'">
      <Block title="数据源管理" :enable-fixed-height="true" @height-changed="onBlockHeightChanged">
        <template #topRight>
          <div class="top-buttons">
            <el-button size="small" type="primary" @click="onClickAdd">新增</el-button>
            <el-button size="small" type="primary" @click="onClickLogManagement">数据源日志管理</el-button>

            <el-dropdown>
              <el-button size="small" type="primary">
                数据备份与恢复<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="onClickBackupRule">数据备份与恢复规则</el-dropdown-item>
                  <el-dropdown-item @click="onClickBackupVerify">数据备份与恢复验证</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <el-dropdown>
              <el-button size="small" type="primary">
                数据源审计<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="onClickPermissionAudit">数据源权限审计</el-dropdown-item>
                  <el-dropdown-item @click="onClickAccessAudit">数据源访问审计</el-dropdown-item>
                  <el-dropdown-item @click="onClickDataSourcePermissionManagement">数据源权限管理</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <el-button size="small" type="primary" @click="onClickDataSyncManagement">
              数据源同步
            </el-button>

            <el-dropdown>
              <el-button size="small" type="primary">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="onClickExceptionHandling">异常处理</el-dropdown-item>
                  <el-dropdown-item @click="onClickQualityReportManagement">质量报告管理</el-dropdown-item>
                  <el-dropdown-item @click="onClickDataSourceIdentification">数据源识别</el-dropdown-item>
                  <el-dropdown-item @click="onClickIntegrationStrategy">集成策略与任务</el-dropdown-item>
                  <el-dropdown-item @click="onClickTransformationRule">转换规则与任务</el-dropdown-item>
                  <el-dropdown-item @click="onClickFormatFileConfig">格式文件配置</el-dropdown-item>
                  <el-dropdown-item @click="onClickDataSourceAccessRule">数据源接入规则</el-dropdown-item>
                  <el-dropdown-item @click="onClickAccessAnalysisMonitor">访问与分析监控</el-dropdown-item>
                  <el-dropdown-item @click="onClickDataSourceHealthMonitor">数据源健康监控</el-dropdown-item>
                  <el-dropdown-item @click="onClickSourceAnalysis">来源分析</el-dropdown-item>
                  <el-dropdown-item @click="onClickArchiveManagement">归档</el-dropdown-item>
                  <el-dropdown-item @click="onClickDataSourceBloodlineAnalysis">血缘分析与影响</el-dropdown-item>
                  <el-dropdown-item @click="onClickVersionHistory">版本历史记录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>

        <template #expand>
          <!-- 搜索 -->
          <div class="search">
            <div class="search-row">
              <div class="search-fields">
                <el-form :model="searchForm" inline>
                  <el-form-item label="数据源版本" label-width="100px">
                    <el-input v-model="searchForm.version" placeholder="请输入版本" size="small" style="width: 160px" clearable />
                  </el-form-item>
                  <el-form-item label="数据源名称" label-width="150px">
                    <el-input v-model="searchForm.name" placeholder="请输入名称" size="small" style="width: 160px" clearable />
                  </el-form-item>
                  <el-form-item label="数据源类型" label-width="150px">
                    <el-input v-model="searchForm.sourceType" placeholder="请输入类型" size="small" style="width: 160px" clearable />
                  </el-form-item>
                </el-form>
              </div>
              <div class="search-buttons">
                <el-button size="small" type="primary" @click="onSearch">查询</el-button>
                <el-button size="small" @click="onReset">重置</el-button>
                <el-button size="small" @click="$router.back()">返回</el-button>
              </div>
            </div>
          </div>
        </template>

        <!-- 列表 -->
        <TableV2
          :defaultTableData="getFilteredData()"
          :columns="columns"
          :enable-toolbar="false"
          :enable-own-button="false"
          :height="tableHeight"
          :buttons="buttons"
          :loading="loading"
          @click-button="onTableClickButton"
        />

        <!-- 分页 -->
        <Pagination
          :total="pagination.total"
          :current-page="pagination.page"
          :page-size="pagination.size"
          @current-change="onPaginationChange($event, 'page')"
          @size-change="onPaginationChange($event, 'size')"
        />
      </Block>
    </div>

    <!-- 数据集Tab内容 -->
    <div v-if="activeTab === 'dataSet'">
      <DataSetTab :data-source-list="dataSourceList" />
    </div>

    <!-- 用户反馈Tab内容 -->
    <div v-if="activeTab === 'userFeedback'">
      <UserFeedbackTab />
    </div>

    <!-- 接口类型Tab内容 -->
    <div v-if="activeTab === 'interfaceType'">
      <InterfaceTypeTab />
    </div>

    <!-- 数据接入Tab内容 -->
    <div v-if="activeTab === 'dataAccess'">
      <DataAccess />
    </div>

    <!-- 其他Tab的占位内容 -->
    <div v-show="activeTab !== 'dataSource' && activeTab !== 'dataSet' && activeTab !== 'userFeedback' && activeTab !== 'interfaceType' && activeTab !== 'dataAccess'" class="tab-placeholder">
      <el-empty description="该功能正在开发中..." />
    </div>

    <!-- 新增/编辑/详情弹窗 -->
    <Dialog
      width="35%"
      v-model="showDialogForm"
      :title="dialogMode === 'add' ? '新增数据源' : dialogMode === 'edit' ? '编辑数据源' : '数据源详情'"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="保存中"
      @closed="currentRow = null; dialogForm = {}; selectedDataSourceCategory = ''; selectedDataSourceTypes = []; selectedDatabase = ''"
      @click-confirm="onDialogConfirm"
    >
      <!-- 基础信息表单 -->
      <Form
        ref="dialogFormRef"
        v-model="dialogForm"
        :props="dialogFormProps"
        :rules="dialogFormRules"
        :enable-button="false"
      />

      <!-- 数据源类型选择 -->
      <div class="form-section">
        <h3 class="section-title">数据源类型（单选）</h3>

        <!-- API数据源类型 -->
        <div class="data-source-type-group">
          <div class="type-group-header">
            <el-radio v-model="selectedDataSourceCategory" value="api" :disabled="dialogMode === 'detail'" @change="onDataSourceCategoryChange('api')">
              {{ dataSourceTypes.api.label }}
            </el-radio>
          </div>
          <div v-if="selectedDataSourceCategory === 'api'" class="type-options">
            <el-checkbox-group v-model="selectedDataSourceTypes" class="checkbox-group">
              <el-checkbox v-for="option in dataSourceTypes.api.options" :key="option.value" :label="option.value" :disabled="dialogMode === 'detail'">
                {{ option.label }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>

        <!-- 文件数据源类型 -->
        <div class="data-source-type-group">
          <div class="type-group-header">
            <el-radio v-model="selectedDataSourceCategory" value="file" :disabled="dialogMode === 'detail'" @change="onDataSourceCategoryChange('file')">
              {{ dataSourceTypes.file.label }}
            </el-radio>
          </div>
          <div v-if="selectedDataSourceCategory === 'file'" class="type-options">
            <el-checkbox-group v-model="selectedDataSourceTypes" class="checkbox-group">
              <el-checkbox v-for="option in dataSourceTypes.file.options" :key="option.value" :label="option.value" :disabled="dialogMode === 'detail'">
                {{ option.label }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>

        <!-- 数据流数据源类型 -->
        <div class="data-source-type-group">
          <div class="type-group-header">
            <el-radio v-model="selectedDataSourceCategory" value="stream" :disabled="dialogMode === 'detail'" @change="onDataSourceCategoryChange('stream')">
              {{ dataSourceTypes.stream.label }}
            </el-radio>
          </div>
          <div v-if="selectedDataSourceCategory === 'stream'" class="type-options">
            <el-checkbox-group v-model="selectedDataSourceTypes" class="checkbox-group">
              <el-checkbox v-for="option in dataSourceTypes.stream.options" :key="option.value" :label="option.value" :disabled="dialogMode === 'detail'">
                {{ option.label }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </div>

      <!-- 数据库选择 -->
      <div class="form-section">
        <h3 class="section-title">数据库（单选）</h3>
        <el-radio-group v-model="selectedDatabase" class="database-selection">
          <el-radio
            v-for="database in databaseOptions"
            :key="database"
            :label="database"
            :disabled="dialogMode === 'detail' ? !hasConfigForDatabase(database) : false"
            @change="onDatabaseSelect(database)"
          >
            <span
              :class="{
                'clickable-in-detail': dialogMode === 'detail' && hasConfigForDatabase(database),
                'no-config': dialogMode === 'detail' && !hasConfigForDatabase(database)
              }"
              @click="dialogMode === 'detail' && hasConfigForDatabase(database) && onDatabaseSelect(database)"
            >
              {{ database }}
              <span v-if="dialogMode === 'detail' && hasConfigForDatabase(database)" class="view-config-hint">（点击查看配置）</span>
              <span v-if="dialogMode === 'detail' && !hasConfigForDatabase(database)" class="no-config-hint">（暂无配置）</span>
            </span>
          </el-radio>
        </el-radio-group>
      </div>

      <!-- 自定义 footer 插槽 -->
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialogForm = false">关闭</el-button>
          <el-button v-if="dialogMode !== 'detail'" type="primary" :loading="loading" @click="onDialogConfirm">确定</el-button>
        </div>
      </template>
    </Dialog>

    <!-- 数据库配置弹窗 -->
    <Dialog
      width="30%"
      v-model="showDatabaseConfigDialog"
      :title="`${selectedDatabaseType} 数据库配置`"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="保存中"
      @closed="databaseConfigForm = { connectionUrl: '', port: '', username: '', password: '', databaseName: '', authType: '' }"
      @click-confirm="onDatabaseConfigConfirm"
    >
      <Form
        ref="databaseConfigFormRef"
        v-model="databaseConfigForm"
        :props="getDatabaseConfigFormProps()"
        :rules="getDatabaseConfigFormRules()"
        :enable-button="false"
      />

      <!-- 自定义 footer 插槽 -->
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDatabaseConfigDialog = false">关闭</el-button>
          <el-button v-if="dialogMode !== 'detail'" type="primary" :loading="loading" @click="onDatabaseConfigConfirm">确定</el-button>
        </div>
      </template>
    </Dialog>

    <!-- 数据源日志管理弹窗 -->
    <Dialog
      v-model="showLogDialog"
      title="数据源日志管理"
      :destroy-on-close="true"
      width="700px"
      :visible-confirm-button="false"
      cancel-text="关闭"
      @click-cancel="showLogDialog = false"
    >
      <div class="log-dialog-content">
        <!-- 搜索区域 -->
        <div class="log-search">
          <el-form :model="logSearchForm" inline>
            <el-form-item label="数据源">
              <el-input v-model="logSearchForm.dataSource" placeholder="请输入数据源" size="small" style="width: 200px" clearable />
            </el-form-item>
            <el-form-item label="操作时间">
              <el-input v-model="logSearchForm.operationTime" placeholder="请输入时间" size="small" style="width: 200px" clearable />
            </el-form-item>
            <el-form-item>
              <el-button size="small" type="primary" @click="onLogSearch">查询</el-button>
              <el-button size="small" @click="onLogReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 日志表格 -->
        <div class="log-table">
          <el-table :data="getFilteredLogs()" border style="width: 100%">
            <el-table-column prop="sequence" label="序号" width="65" align="center" />
            <el-table-column prop="operatorUser" label="操作用户" width="120" align="center" />
            <el-table-column prop="operationType" label="操作类型" width="120" align="center">
              <template #default="scope">
                <el-tag
                  :type="scope.row.operationType === '查看' ? 'info' :
                        scope.row.operationType === '新增' ? 'success' :
                        scope.row.operationType === '编辑' ? 'warning' : 'danger'"
                >
                  {{ scope.row.operationType }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="operationDataSource" label="操作数据源" width="200" align="center" />
            <el-table-column prop="operationTime" label="操作时间" width="150" align="center" />
          </el-table>
        </div>


      </div>
    </Dialog>

    <!-- 数据备份与恢复规则弹窗 -->
    <Dialog
      v-model="showBackupRuleDialog"
      title="数据源备份与恢复"
      :destroy-on-close="true"
      width="800px"
      :visible-confirm-button="false"
      cancel-text="关闭"
      @click-cancel="showBackupRuleDialog = false"
    >
      <div class="dialog-content">
        <!-- 数据源备份规则 -->
        <div class="section-title">数据源备份规则</div>
        <div class="form-section">
          <el-form :model="backupRuleForm" label-width="180px">
            <el-form-item label="数据备份开始时间：">
              <el-date-picker
                v-model="backupRuleForm.backupStartTime"
                type="datetime"
                placeholder="请选择日期时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>

            <el-form-item label="全量备份频率：">
              <el-select v-model="backupRuleForm.fullBackupFrequency" style="width: 100%">
                <el-option label="每小时" value="每小时" />
                <el-option label="每日" value="每日" />
                <el-option label="每周" value="每周" />
                <el-option label="每月" value="每月" />
              </el-select>
            </el-form-item>

            <el-form-item label="增量备份频率：">
              <el-select v-model="backupRuleForm.incrementalBackupFrequency" style="width: 100%">
                <el-option label="每小时" value="每小时" />
                <el-option label="每日" value="每日" />
                <el-option label="每周" value="每周" />
                <el-option label="每月" value="每月" />
              </el-select>
            </el-form-item>

            <el-form-item label="备份数据清理策略：">
              <el-select v-model="backupRuleForm.dataCleanupPolicy" style="width: 100%">
                <el-option label="保留30天" value="保留30天" />
                <el-option label="保留60天" value="保留60天" />
                <el-option label="保留120天" value="保留120天" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>

        <!-- 数据源恢复启动规则 -->
        <div class="section-title">数据源恢复启动规则</div>
        <div class="form-section">
          <el-form :model="backupRuleForm" label-width="180px">
            <el-form-item label="请选择恢复至指定时期：">
              <el-date-picker
                v-model="backupRuleForm.recoveryToTime"
                type="datetime"
                placeholder="请选择日期时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>

            <el-form-item label="请选择恢复执行时间：">
              <el-date-picker
                v-model="backupRuleForm.recoveryExecutionTime"
                type="datetime"
                placeholder="请选择日期时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showBackupRuleDialog = false">取消</el-button>
          <el-button type="primary" @click="showBackupRuleDialog = false">确定</el-button>
        </div>
      </template>
    </Dialog>

    <!-- 数据备份与恢复验证弹窗 -->
    <Dialog
      v-model="showBackupVerifyDialog"
      title="数据源备份与恢复验证"
      :destroy-on-close="true"
      width="600px"
      :visible-confirm-button="false"
    >
      <div class="dialog-content">
        <el-form :model="backupVerifyForm" label-width="80px">
          <el-form-item label="类型：">
            <el-select v-model="backupVerifyForm.type" style="width: 100%">
              <el-option label="数据备份验证" value="数据备份验证" />
              <el-option label="数据恢复验证" value="数据恢复验证" />
            </el-select>
          </el-form-item>

          <el-form-item label="数据源：">
            <el-select v-model="backupVerifyForm.dataSource" placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in dataSourceList"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
          </el-form-item>

          <!-- 恢复点字段，仅在数据恢复验证时显示 -->
          <el-form-item v-if="backupVerifyForm.type === '数据恢复验证'" label="恢复点：">
            <el-date-picker
              v-model="backupVerifyForm.recoveryPoint"
              type="datetime"
              placeholder="请选择年月日"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-form>

        <!-- 验证按钮 -->
        <div style="text-align: center; margin: 20px 0;">
          <el-button
            type="primary"
            style="width: 100%;"
            @click="executeBackupVerify"
          >
            {{ backupVerifyForm.type === '数据备份验证' ? '验证备份' : '验证恢复' }}
          </el-button>
        </div>

        <!-- 验证状态 -->
        <div v-if="verifyResult" style="margin-top: 20px;">
          <div style="font-weight: bold; margin-bottom: 10px;">验证状态：</div>
          <div style="background-color: #f5f7fa; padding: 15px; border-radius: 4px; min-height: 80px;">
            {{ verifyResult }}
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showBackupVerifyDialog = false">取消</el-button>
        </div>
      </template>
    </Dialog>

    <!-- 访问权限调整弹窗 -->
    <Dialog
      v-model="showAccessPermissionDialog"
      title="访问权限调整"
      :destroy-on-close="true"
      width="600px"
      :visible-confirm-button="false"
    >
      <div class="dialog-content">
        <el-form :model="accessPermissionForm" label-width="100px">
          <el-form-item label="对象类型：">
            <el-select v-model="accessPermissionForm.objectType" style="width: 100%">
              <el-option label="用户" value="用户" />
              <el-option label="角色" value="角色" />
            </el-select>
          </el-form-item>

          <el-form-item label="用户/角色：">
            <el-select v-model="accessPermissionForm.objectSelection" placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in getObjectSelectionData"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="权限范围：">
            <div style="display: flex; flex-wrap: wrap; gap: 20px;">
              <el-checkbox v-model="accessPermissionForm.readPermission">库级权限</el-checkbox>
              <el-checkbox v-model="accessPermissionForm.tablePermission">表级权限</el-checkbox>
              <el-checkbox v-model="accessPermissionForm.fieldPermission">字段权限</el-checkbox>
            </div>
          </el-form-item>

          <el-form-item label="权限设置：">
            <div style="display: flex; flex-wrap: wrap; gap: 20px;">
              <el-checkbox v-model="accessPermissionForm.viewPermission">查看</el-checkbox>
              <el-checkbox v-model="accessPermissionForm.editPermission">编辑</el-checkbox>
              <el-checkbox v-model="accessPermissionForm.addPermission">新增</el-checkbox>
              <el-checkbox v-model="accessPermissionForm.deletePermission">删除</el-checkbox>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAccessPermissionDialog = false">取消</el-button>
          <el-button type="primary" @click="saveAccessPermission">确定</el-button>
        </div>
      </template>
    </Dialog>

    <!-- 数据源权限审计弹窗 -->
    <Dialog
      v-model="showPermissionAuditDialog"
      title="数据源权限审计"
      :destroy-on-close="true"
      width="890px"
      :visible-confirm-button="false"
    >
      <div class="dialog-content">
        <!-- 统计区域 -->
        <div style="margin-bottom: 30px;">
          <div style="font-size: 16px; font-weight: bold; margin-bottom: 15px; color: #333;">统计概览</div>
          <div style="display: flex; gap: 30px;">
            <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; min-width: 120px;">
              <div style="font-size: 24px; font-weight: bold; color: #409EFF;">{{ permissionAuditStats.dataSourceCount }}</div>
              <div style="color: #666; margin-top: 5px;">数据源总数</div>
            </div>
            <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; min-width: 120px;">
              <div style="font-size: 24px; font-weight: bold; color: #67C23A;">{{ permissionAuditStats.authorizedUserCount }}</div>
              <div style="color: #666; margin-top: 5px;">授权用户数</div>
            </div>
            <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; min-width: 120px;">
              <div style="font-size: 24px; font-weight: bold; color: #E6A23C;">{{ permissionAuditStats.permissionItemCount }}</div>
              <div style="color: #666; margin-top: 5px;">权限项数</div>
            </div>
          </div>
        </div>

        <!-- 明细列表 -->
        <div>
          <div style="font-size: 16px; font-weight: bold; margin-bottom: 15px; color: #333;">明细</div>
          <el-table :data="permissionAuditData" style="width: 100%" max-height="400">
            <el-table-column prop="dataSourceName" label="数据源名称" width="150" />
            <el-table-column prop="objectName" label="用户名称" width="120" />
            <el-table-column label="查看权限" width="100" align="center">
              <template #default="{ row }">
                <el-tag v-if="row.viewPermission" type="success" size="small">是</el-tag>
                <el-tag v-else type="info" size="small">否</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="编辑权限" width="100" align="center">
              <template #default="{ row }">
                <el-tag v-if="row.editPermission" type="success" size="small">是</el-tag>
                <el-tag v-else type="info" size="small">否</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="新增权限" width="100" align="center">
              <template #default="{ row }">
                <el-tag v-if="row.addPermission" type="success" size="small">是</el-tag>
                <el-tag v-else type="info" size="small">否</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="删除权限" width="100" align="center">
              <template #default="{ row }">
                <el-tag v-if="row.deletePermission" type="success" size="small">是</el-tag>
                <el-tag v-else type="info" size="small">否</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="授权时间" width="180" />
          </el-table>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showPermissionAuditDialog = false">取消</el-button>
        </div>
      </template>
    </Dialog>

    <!-- 数据源访问审计弹窗 -->
    <Dialog
      v-model="showAccessAuditDialog"
      title="数据源访问审计"
      :destroy-on-close="true"
      width="1000px"
      :visible-confirm-button="false"
    >
      <div class="dialog-content">
      

        <!-- 搜索区域 -->
        <div style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
          <el-form :model="accessAuditSearchForm" :inline="true" label-width="80px">
            <el-form-item label="数据源">
              <el-select v-model="accessAuditSearchForm.dataSource" placeholder="请选择数据源" clearable style="width: 150px;">
                <el-option label="全部" value="" />
                <el-option
                  v-for="item in dataSourceList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="操作类型">
              <el-select v-model="accessAuditSearchForm.operationType" placeholder="请选择操作类型" clearable style="width: 120px;">
                <el-option label="全部" value="" />
                <el-option label="查看" value="查看" />
                <el-option label="新增" value="新增" />
                <el-option label="编辑" value="编辑" />
                <el-option label="删除" value="删除" />
              </el-select>
            </el-form-item>
            <el-form-item label="操作用户">
              <el-input v-model="accessAuditSearchForm.operatorUser" placeholder="请输入操作用户" clearable style="width: 120px;" />
            </el-form-item>
         
            <el-form-item>
              <el-button type="primary" @click="onAccessAuditSearch">查询</el-button>
              <el-button @click="accessAuditSearchForm = { dataSource: '', operationType: '', operatorUser: '', startTime: '', endTime: '' }">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-table :data="filteredAccessAuditData" style="width: 100%" max-height="400">
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="operationTime" label="操作时间" width="180" />
          <el-table-column prop="operationDataSource" label="数据源" width="150" />
          <el-table-column prop="operationType" label="操作类型" width="120" />
          <el-table-column prop="operatorUser" label="操作用户" width="120" />
          <el-table-column prop="accessTime" label="访问时间" width="180" />
          <el-table-column prop="accessUser" label="访问对象" width="150">
            <template #header>
              <span>访问对象</span>
              <el-tooltip content="功能对象/接口对象/数据库对象" placement="top">
                <el-icon style="margin-left: 4px; color: #409EFF;"><QuestionFilled /></el-icon>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="ipAddress" label="IP地址" width="140" />
        </el-table>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAccessAuditDialog = false">取消</el-button>
        </div>
      </template>
    </Dialog>

    <!-- 数据源同步管理弹窗 -->
    <Dialog
      v-model="showDataSyncDialog"
      title="数据源同步管理"
      width="900px"
      :destroy-on-close="true"
      :enable-footer="false"
    >
      <div class="data-sync-management">
        <!-- 搜索区域 -->
        <div class="search-section" style="margin-bottom: 20px; display: flex; align-items: center; gap: 16px;">
          <div style="display: flex; align-items: center;">
            <span style="margin-right: 8px; font-size: 14px;">任务名称</span>
            <el-input v-model="dataSyncSearchForm.name" placeholder="请输入任务名称" size="small" style="width: 160px" clearable />
          </div>
          <div style="display: flex; align-items: center;">
            <span style="margin-right: 8px; font-size: 14px;">源数据源</span>
            <el-select v-model="dataSyncSearchForm.sourceDataSource" placeholder="请选择源数据源" size="small" style="width: 160px" clearable>
              <el-option v-for="option in dataSyncSearchFormProp[1].options" :key="option.value" :label="option.label" :value="option.value" />
            </el-select>
          </div>
          <div style="display: flex; align-items: center;">
            <span style="margin-right: 8px; font-size: 14px;">状态</span>
            <el-select v-model="dataSyncSearchForm.status" placeholder="请选择状态" size="small" style="width: 160px" clearable>
              <el-option v-for="option in dataSyncSearchFormProp[2].options" :key="option.value" :label="option.label" :value="option.value" />
            </el-select>
          </div>
          <div style="display: flex; gap: 8px;">
            <el-button size="small" type="primary" @click="onDataSyncSearch">查询</el-button>
            <el-button size="small" @click="onDataSyncReset">清空</el-button>
          </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="action-section" style="margin-bottom: 20px;">
          <el-button size="small" type="primary" @click="onClickAddDataSync">新增</el-button>
        </div>

        <!-- 数据列表 -->
        <div class="table-section">
          <el-table
            :data="filteredDataSyncList"
            style="width: 100%"
            max-height="400"
            border
          >
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="name" label="任务名称" width="150" />
            <el-table-column prop="sourceDataSource" label="源数据源" width="150" />
            <el-table-column prop="targetDataSource" label="目标数据源" width="150" />
            <el-table-column prop="syncType" label="同步类型" width="120" />
            <el-table-column prop="syncFrequency" label="同步频率" width="120" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status ? 'success' : 'danger'" size="small">
                  {{ row.status ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="180" />
            <el-table-column prop="updateTime" label="更新时间" width="180" />
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <el-button
                  size="small"
                  type="primary"
                  @click="onDataSyncTableClickButton({row, btn: {code: 'edit'}})"
                >
                  编辑
                </el-button>
                <el-popconfirm
                  title="确认删除吗?"
                  @confirm="onDataSyncTableClickButton({row, btn: {code: 'delete'}})"
                >
                  <template #reference>
                    <el-button size="small" type="danger">删除</el-button>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </Dialog>

    <!-- 数据源同步配置弹窗 -->
    <Dialog
      v-model="showDataSyncFormDialog"
      :title="currentDataSyncRow ? '编辑数据源同步配置' : '新增数据源同步配置'"
      width="600px"
      :destroy-on-close="true"
      :loading="dataSyncLoading"
      loading-text="保存中"
      @closed="currentDataSyncRow = null; dataSyncForm = {}"
      @click-confirm="onDataSyncFormConfirm"
    >
      <Form
        ref="dataSyncFormRef"
        v-model="dataSyncForm"
        :props="dataSyncFormProps"
        :rules="dataSyncFormRules"
        :enable-button="false"
      ></Form>
    </Dialog>

    <!-- 数据源审计弹窗 -->
    <Dialog
      v-model="showDataSourceAuditDialog"
      title="数据源审计"
      width="600px"
      :destroy-on-close="true"
      :enable-footer="false"
    >
      <div class="data-source-audit">
        <el-tabs>
          <el-tab-pane label="权限审计">
            <el-button type="primary" size="small" @click="onClickPermissionAudit">查看权限审计</el-button>
          </el-tab-pane>
          <el-tab-pane label="访问审计">
            <el-button type="primary" size="small" @click="onClickAccessAudit">查看访问审计</el-button>
          </el-tab-pane>
        </el-tabs>
      </div>
    </Dialog>

    <!-- 异常处理弹窗 -->
    <Dialog
      v-model="showExceptionDialog"
      title="数据源异常处理"
      width="800px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      cancel-text="关闭"
      @click-cancel="showExceptionDialog = false"
    >
      <div class="exception-dialog-content">
        <!-- 列表 -->
        <TableV2
          :defaultTableData="exceptionList"
          :columns="[
            { prop: 'dataSourceName', label: '数据源名称' },
            { prop: 'exceptionType', label: '异常类型' },
            { prop: 'exceptionTime', label: '异常时间' },
            { prop: 'status', label: '状态' },
            { prop: 'operation', label: '操作' }
          ]"
          :enable-toolbar="false"
          :enable-own-button="false"
          :enable-selection="false"
          height="400"
          :loading="loading"
        />
      </div>
    </Dialog>

    <!-- 质量报告管理弹窗 -->
    <Dialog
      v-model="showQualityReportDialog"
      title="数据源质量管理"
      width="900px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      cancel-text="关闭"
      @click-cancel="showQualityReportDialog = false"
    >
      <div class="quality-report-dialog-content">
        <el-tabs v-model="qualityReportActiveTab">
          <!-- 质量报告生成 Tab -->
          <el-tab-pane label="质量报告生成" name="generation">
            <div style="margin-bottom: 20px;">
              <el-button size="small" type="primary" @click="onClickAddQualityReport">新增质量报告</el-button>
            </div>
            <TableV2
              :defaultTableData="qualityReportList"
              :columns="[
                { prop: 'sequence', label: '序号', width: 80 },
                { prop: 'reportName', label: '报告名称' },
                { prop: 'dataSource', label: '数据源' },
                { prop: 'createTime', label: '创建时间' },
                { prop: 'status', label: '状态' }
              ]"
              :enable-toolbar="false"
              :enable-own-button="false"
              :enable-selection="false"
              height="350"
              :loading="loading"
              :buttons="[]"
              @click-button="({ row, btn }) => onQualityReportAction(row)"
            >
              <template #action="{ row }">
                <el-button size="small" type="primary" @click="onQualityReportAction(row)">
                  {{ row.buttonText }}
                </el-button>
              </template>
            </TableV2>
          </el-tab-pane>

          <!-- 质量改进 Tab -->
          <el-tab-pane label="质量改进" name="improvement">
            <div style="margin-bottom: 20px;">
              <el-button size="small" type="primary" @click="onClickAddQualityImprovement">新增质量改进</el-button>
            </div>
            <TableV2
              :defaultTableData="qualityImprovementList"
              :columns="[
                { prop: 'sequence', label: '序号', width: 80 },
                { prop: 'dataSource', label: '数据源' },
                { prop: 'improvementDescription', label: '改进说明' },
                { prop: 'responsible', label: '责任人' },
                { prop: 'status', label: '状态' },
                { prop: 'createTime', label: '创建时间' }
              ]"
              :enable-toolbar="false"
              :enable-own-button="false"
              :enable-selection="false"
              height="350"
              :loading="loading"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </Dialog>

    <!-- 新增质量报告弹窗 -->
    <Dialog
      v-model="showAddQualityReportDialog"
      title="新增质量报告"
      width="500px"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="保存中"
      @closed="qualityReportForm = {}"
      @click-confirm="onConfirmAddQualityReport"
    >
      <Form
        ref="qualityReportFormRef"
        v-model="qualityReportForm"
        :props="[
          { label: '报告名称', prop: 'reportName', type: 'text', required: true },
          { label: '数据源', prop: 'dataSource', type: 'select', required: true, options: getDataSourceOptions() }
        ]"
        :rules="{
          reportName: [{ required: true, message: '请输入报告名称', trigger: 'blur' }],
          dataSource: [{ required: true, message: '请选择数据源', trigger: 'change' }]
        }"
        :enable-button="false"
      />
    </Dialog>

    <!-- 新增质量改进弹窗 -->
    <Dialog
      v-model="showAddQualityImprovementDialog"
      title="新增质量改进"
      width="500px"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="保存中"
      @closed="qualityImprovementForm = {}"
      @click-confirm="onConfirmAddQualityImprovement"
    >
      <Form
        ref="qualityImprovementFormRef"
        v-model="qualityImprovementForm"
        :props="[
          { label: '数据源', prop: 'dataSource', type: 'select', required: true, options: getDataSourceOptions() },
          { label: '改进说明', prop: 'improvementDescription', type: 'textarea', required: true },
          { label: '责任人', prop: 'responsible', type: 'select', required: true, options: responsibleOptions }
        ]"
        :rules="{
          dataSource: [{ required: true, message: '请选择数据源', trigger: 'change' }],
          improvementDescription: [{ required: true, message: '请输入改进说明', trigger: 'blur' }],
          responsible: [{ required: true, message: '请选择责任人', trigger: 'change' }]
        }"
        :enable-button="false"
      />
    </Dialog>

    <!-- 质量报告分析弹窗 -->
    <Dialog
      v-model="showReportAnalysisDialog"
      title="质量报告分析"
      width="800px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      cancel-text="关闭"
      @click-cancel="showReportAnalysisDialog = false"
    >
      <div class="report-analysis-content" v-if="currentAnalysisReport">
        <!-- 统计卡片 -->
        <div class="stats-cards" style="display: flex; gap: 20px; margin-bottom: 30px;">
          <div class="stat-card" style="flex: 1; text-align: center; padding: 20px; background: #f0f9ff; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #1890ff;">98/100</div>
            <div style="color: #666; margin-top: 5px;">数据质量</div>
          </div>
          <div class="stat-card" style="flex: 1; text-align: center; padding: 20px; background: #f6ffed; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #52c41a;">95%</div>
            <div style="color: #666; margin-top: 5px;">完整性</div>
          </div>
          <div class="stat-card" style="flex: 1; text-align: center; padding: 20px; background: #fff7e6; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #fa8c16;">90%</div>
            <div style="color: #666; margin-top: 5px;">准确性</div>
          </div>
        </div>

        <!-- 图表分析 -->
        <div class="chart-section" style="margin-bottom: 30px;">
          <h4 style="margin-bottom: 15px;">质量分析</h4>
          <div style="display: flex; gap: 20px;">
            <!-- 饼图 -->
            <div style="flex: 1; height: 300px; border: 1px solid #e8e8e8; border-radius: 8px; padding: 20px;">
              <div style="text-align: center; margin-bottom: 20px; font-weight: bold;">数据分布</div>
              <div ref="pieChartRef" style="width: 100%; height: 200px;"></div>
            </div>
            <!-- 图例 -->
            <div style="flex: 1; padding: 20px;">
              <div style="margin-bottom: 10px;"><span style="display: inline-block; width: 12px; height: 12px; background: #1890ff; margin-right: 8px;"></span>正常数据 85%</div>
              <div style="margin-bottom: 10px;"><span style="display: inline-block; width: 12px; height: 12px; background: #52c41a; margin-right: 8px;"></span>异常数据 10%</div>
              <div style="margin-bottom: 10px;"><span style="display: inline-block; width: 12px; height: 12px; background: #fa8c16; margin-right: 8px;"></span>缺失数据 5%</div>
            </div>
          </div>
        </div>

        <!-- 字段质量分析表格 -->
        <div class="field-analysis-section">
          <h4 style="margin-bottom: 15px;">字段质量分析</h4>
          <TableV2
            :defaultTableData="[
              { fieldName: 'user_id', completeness: '100%', accuracy: '98%' },
              { fieldName: 'user_name', completeness: '95%', accuracy: '92%' },
              { fieldName: 'email', completeness: '90%', accuracy: '88%' },
              { fieldName: 'phone', completeness: '85%', accuracy: '90%' }
            ]"
            :columns="[
              { prop: 'fieldName', label: '字段名' },
              { prop: 'completeness', label: '完整度' },
              { prop: 'accuracy', label: '准确度' }
            ]"
            :enable-toolbar="false"
            :enable-own-button="false"
            :enable-selection="false"
            height="200"
          />
        </div>
      </div>
    </Dialog>

    <!-- 数据源识别弹窗 -->
    <Dialog
      v-model="showDataSourceIdentificationDialog"
      title="数据源识别"
      width="1000px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      cancel-text="关闭"
      @click-cancel="showDataSourceIdentificationDialog = false"
    >
      <div class="identification-dialog-content">
        <!-- 统计卡片 -->
        <div class="stats-section" style="display: flex; gap: 20px; margin-bottom: 30px;">
          <div class="stat-card" style="flex: 1; text-align: center; padding: 20px; background: #f0f9ff; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #1890ff;">{{ identificationStats.total }}</div>
            <div style="color: #666; margin-top: 5px;">数据源总数</div>
          </div>
          <div class="stat-card" style="flex: 1; text-align: center; padding: 20px; background: #f6ffed; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #52c41a;">{{ identificationStats.identified }}</div>
            <div style="color: #666; margin-top: 5px;">已识别</div>
          </div>
          <div class="stat-card" style="flex: 1; text-align: center; padding: 20px; background: #fff7e6; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #fa8c16;">{{ identificationStats.unidentified }}</div>
            <div style="color: #666; margin-top: 5px;">未识别</div>
          </div>
          <div class="stat-card" style="flex: 1; text-align: center; padding: 20px; background: #fff2f0; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #ff4d4f;">{{ identificationStats.sensitive }}</div>
            <div style="color: #666; margin-top: 5px;">敏感数据源</div>
          </div>
        </div>

        <!-- 识别明细 -->
        <div class="detail-section">
          <h4 style="margin-bottom: 15px;">识别明细</h4>
          <TableV2
            :defaultTableData="identificationDetailList"
            :columns="[
              { prop: 'sequence', label: '序号', width: 80 },
              { prop: 'dataSourceName', label: '数据源名称' },
              { prop: 'sensitivity', label: '敏感度' },
              { prop: 'type', label: '类型' },
              { prop: 'description', label: '描述' },
              { prop: 'identificationTime', label: '识别时间' }
            ]"
            :enable-toolbar="false"
            :enable-own-button="false"
            :enable-selection="false"
            height="400"
            :loading="loading"
          />
        </div>
      </div>
    </Dialog>

    <!-- 集成策略与任务弹窗 -->
    <IntegrationStrategyDialog
      v-model="showIntegrationStrategyDialog"
      :data-source-list="dataSourceList"
    />

    <!-- 转换规则与任务弹窗 -->
    <TransformationRuleDialog
      v-model="showTransformationRuleDialog"
    />

    <!-- 格式文件配置弹窗 -->
    <FormatFileConfigDialog
      v-model="showFormatFileConfigDialog"
    />

    <!-- 数据源接入规则弹窗 -->
    <DataSourceAccessRuleDialog
      v-model="showDataSourceAccessRuleDialog"
    />

    <!-- 访问与分析监控弹窗 -->
    <AccessAnalysisMonitorDialog
      v-model="showAccessAnalysisMonitorDialog"
    />

    <!-- 数据源健康监控弹窗 -->
    <DataSourceHealthMonitorDialog
      v-model="showDataSourceHealthMonitorDialog"
    />

    <!-- 来源分析弹窗 -->
    <SourceAnalysisDialog
      v-model="showSourceAnalysisDialog"
    />

    <!-- 归档管理弹窗 -->
    <ArchiveManagementDialog
      v-model="showArchiveManagementDialog"
    />

    <!-- 数据源权限验证弹窗 -->
    <DataSourcePermissionVerificationDialog
      v-model="showDataSourcePermissionVerificationDialog"
    />

    <!-- 数据源容量配置弹窗 -->
    <DataSourceCapacityConfigDialog
      v-model="showDataSourceCapacityConfigDialog"
    />

    <!-- 数据源解压规则配置弹窗 -->
    <DataSourceDecompressionRuleDialog
      v-model="showDataSourceDecompressionRuleDialog"
    />

    <!-- 数据清理与迁移配置弹窗 -->
    <DataCleaningAndMigrationDialog
      v-model="showDataCleaningAndMigrationDialog"
    />

    <!-- 清洗任务弹窗 -->
    <DataCleaningTaskDialog
      v-model="showDataCleaningTaskDialog"
    />

    <!-- 规则配置弹窗 -->
    <RuleConfigDialog
      v-model="showRuleConfigDialog"
    />

    <!-- 合规性检查弹窗 -->
    <ComplianceCheckDialog
      v-model="showComplianceCheckDialog"
    />

    <!-- 数据源血缘分析弹窗 -->
    <DataSourceBloodlineAnalysisDialog
      v-model="showDataSourceBloodlineAnalysisDialog"
      :data-source-list="dataSourceList"
    />

    <!-- 版本历史记录弹窗 -->
    <VersionHistoryDialog
      ref="versionHistoryDialogRef"
      v-model="showVersionHistoryDialog"
      :data-source-list="dataSourceList"
    />

    <!-- 数据源权限管理弹窗 -->
    <Dialog
      v-model="showDataSourcePermissionManagementDialog"
      title="数据源权限管理"
      width="1000px"
      :destroy-on-close="true"
      :enable-footer="false"
    >
      <div class="permission-management-content">
        <!-- 顶部搜索区域 -->
        <div class="search-section" style="margin-bottom: 20px; display: flex; align-items: center; gap: 16px;">
          <div style="display: flex; align-items: center;">
            <span style="margin-right: 8px; font-size: 14px;">数据源名称</span>
            <el-input v-model="permissionSearchForm.dataSourceName" placeholder="请输入数据源名称" size="small" style="width: 160px" clearable />
          </div>
          <div style="display: flex; align-items: center;">
            <span style="margin-right: 8px; font-size: 14px;">角色/人员</span>
            <el-input v-model="permissionSearchForm.roleOrPerson" placeholder="请输入角色/人员" size="small" style="width: 160px" clearable />
          </div>
          <div style="display: flex; gap: 8px;">
            <el-button size="small" type="primary" @click="onPermissionSearch">查询</el-button>
            <el-button size="small" @click="onPermissionReset">重置</el-button>
            <el-button size="small" @click="showDataSourcePermissionManagementDialog = false">返回</el-button>
          </div>
        </div>

        <!-- 添加授权按钮 -->
        <div class="action-section" style="margin-bottom: 20px;">
          <el-button size="small" type="primary" @click="showAddPermissionDialog = true">添加授权</el-button>
        </div>

        <!-- 权限列表 -->
        <div class="table-section">
          <el-table
            :data="filteredPermissionManagementList"
            style="width: 100%"
            max-height="400"
            border
          >
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="dataSourceName" label="数据源名称" width="170" />
            <el-table-column prop="authMode" label="授权模式" width="100" />
            <el-table-column prop="roleOrPerson" label="角色/人员" width="180" />
            <el-table-column prop="authPermissions" label="授权权限" width="200">
              <template #default="{ row }">
                <span>{{ row.authPermissions.join('、') }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="authTime" label="授权时间" width="120" />
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <el-button size="small" type="primary" @click="onEditPermission(row)">修改</el-button>
                <el-button size="small" type="danger" @click="onDeletePermission(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </Dialog>

    <!-- 添加授权弹窗 -->
    <Dialog
      v-model="showAddPermissionDialog"
      :title="currentEditPermissionId ? '修改授权' : '添加授权'"
      width="600px"
      :destroy-on-close="true"
      @click-confirm="onAddPermissionConfirm"
      @closed="resetAddPermissionForm"
    >
      <div class="add-permission-content">
        <!-- 数据源选择 -->
        <div class="form-item" style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px; font-weight: bold;">
            <span style="color: red;">*</span>数据源：
          </label>
          <el-select v-model="addPermissionForm.dataSource" placeholder="请选择数据源" style="width: 100%;">
            <el-option
              v-for="dataSource in dataSourceList"
              :key="dataSource.id"
              :label="dataSource.name"
              :value="dataSource.name"
            />
          </el-select>
        </div>

        <!-- 授权模式 -->
        <div class="form-item" style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px; font-weight: bold;">
            <span style="color: red;">*</span>授权模式：
          </label>
          <el-select v-model="addPermissionForm.authMode" style="width: 100%;">
            <el-option label="指定人员" value="指定人员" />
            <el-option label="指定角色" value="指定角色" />
          </el-select>
        </div>

        <!-- 授权内容 -->
        <div class="form-item" style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px; font-weight: bold;">
            <span style="color: red;">*</span>授权内容：
          </label>
          <div style="border: 1px solid #dcdfe6; border-radius: 4px; padding: 12px; min-height: 200px; background: #fafafa;">
            <div style="color: #999; margin-bottom: 12px;">请选择</div>

            <!-- 指定人员模式 -->
            <div v-if="addPermissionForm.authMode === '指定人员'" class="person-selection">
              <div v-for="department in departmentData" :key="department.id" style="margin-bottom: 16px;">
                <!-- 部门 -->
                <div
                  style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer; padding: 8px 12px; border-radius: 4px; background: #f5f5f5; border: 1px solid #e8e8e8;"
                  @click="onDepartmentClick(department)"
                >
                  <el-icon style="margin-right: 8px;">
                    <folder />
                  </el-icon>
                  <span style="font-weight: 500; color: #333;">{{ department.name }}</span>
                  <el-icon style="margin-left: auto; transition: transform 0.3s;" :style="{ transform: department.expanded ? 'rotate(90deg)' : 'rotate(0deg)' }">
                    <arrow-right />
                  </el-icon>
                </div>

                <!-- 人员 - 只在展开时显示 -->
                <div v-if="department.expanded" style="margin-left: 24px; display: flex; flex-wrap: wrap; gap: 8px; margin-top: 8px;">
                  <div
                    v-for="person in department.persons"
                    :key="person"
                    style="padding: 6px 12px; border: 1px solid #d9d9d9; border-radius: 4px; cursor: pointer; font-size: 14px; transition: all 0.3s;"
                    :style="{
                      background: addPermissionForm.selectedPersons.includes(person) ? '#1890ff' : '#fff',
                      color: addPermissionForm.selectedPersons.includes(person) ? '#fff' : '#333',
                      borderColor: addPermissionForm.selectedPersons.includes(person) ? '#1890ff' : '#d9d9d9'
                    }"
                    @click="onPersonClick(person)"
                  >
                    {{ person }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 指定角色模式 -->
            <div v-if="addPermissionForm.authMode === '指定角色'" class="role-selection">
              <div v-for="role in roleData2" :key="role.id" style="margin-bottom: 12px;">
                <div
                  style="display: flex; align-items: center; cursor: pointer; padding: 8px 12px; border-radius: 4px;"
                  :style="{
                    background: addPermissionForm.selectedRoles.includes(role.name) ? '#e6f7ff' : 'transparent',
                    color: addPermissionForm.selectedRoles.includes(role.name) ? '#1890ff' : '#333'
                  }"
                  @click="onRoleClick(role)"
                >
                  <el-icon style="margin-right: 8px;">
                    <folder />
                  </el-icon>
                  <span style="font-weight: 500;">{{ role.name }}</span>
                  <el-icon style="margin-left: auto;">
                    <arrow-right />
                  </el-icon>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作权限 -->
        <div class="form-item">
          <label style="display: block; margin-bottom: 8px; font-weight: bold;">操作权限：</label>
          <div style="display: flex; gap: 16px;">
            <el-checkbox v-model="addPermissionForm.permissions.view">查看</el-checkbox>
            <el-checkbox v-model="addPermissionForm.permissions.add">新增</el-checkbox>
            <el-checkbox v-model="addPermissionForm.permissions.edit">编辑</el-checkbox>
            <el-checkbox v-model="addPermissionForm.permissions.delete">删除</el-checkbox>
          </div>
        </div>
      </div>
    </Dialog>

    <!-- 权限校验遮罩层 -->
    <div
      v-if="permissionVerificationLoading"
      class="permission-verification-overlay"
      style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.3); z-index: 9999; display: flex; align-items: center; justify-content: center;"
    >
      <div style="text-align: center;">
        <el-icon class="loading-icon" style="font-size: 48px; color: #409eff; margin-bottom: 16px;">
          <Loading />
        </el-icon>
        <div style="font-size: 16px; color: white; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);">{{ permissionVerificationText }}</div>
      </div>
    </div>

  </div>
</template>

<route>
{
  meta: {
    title: '数据源管理',
  },
}
</route>

<style scoped lang="scss">
.data-source-management {
  .main-tabs {
    margin-bottom: 16px;

    :deep(.el-tabs__header) {
      margin: 0 0 16px 0;
    }

    :deep(.el-tabs__item) {
      padding: 0 16px;
      margin-right: 8px;
    }
  }

  .top-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .search {
    margin-bottom: 16px;

    .search-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 16px;
    }

    .search-fields {
      flex: 1;
      min-width: 0;
    }

    .search-buttons {
      display: flex;
      gap: 8px;
      flex-shrink: 0;
    }
  }

  .tab-placeholder {
    padding: 40px;
    text-align: center;
  }

  .dialog-content {
    padding: 0 20px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
      border-left: 4px solid #409EFF;
      padding-left: 10px;
    }

    .form-section {
      margin-bottom: 30px;
    }

    .detail-info {
      margin-top: 20px;
    }


  }

  // 日志弹窗样式
  .log-dialog-content {
    .log-search {
      margin-bottom: 20px;
      padding: 16px;
      background: #f5f7fa;
      border-radius: 4px;
    }

    .log-table {
      margin-bottom: 20px;
    }

    .log-dialog-footer {
      text-align: right;
      padding-top: 16px;
      border-top: 1px solid #eee;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px 0 0 0;
  }

  // 表单分区样式
  .form-section {
    margin-top: 20px;
    padding: 15px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;

    .section-title {
      margin: 0 0 15px 0;
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
  }

  // 数据源类型选择样式
  .data-source-type-group {
    margin-bottom: 15px;

    .type-group-header {
      margin-bottom: 10px;
    }

    .type-options {
      margin-left: 24px;

      .checkbox-group {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
      }
    }
  }

  // 数据库选择样式
  .database-selection {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
  }

  // 详情模式下的查看配置提示样式
  .view-config-hint {
    font-size: 12px;
    color: #909399;
    font-style: italic;
    margin-left: 8px;
  }

  // 详情模式下暂无配置的提示样式
  .no-config-hint {
    font-size: 12px;
    color: #f56c6c;
    font-style: italic;
    margin-left: 8px;
  }

  // 详情模式下可点击的数据库选项
  .clickable-in-detail {
    cursor: pointer;

    &:hover {
      color: #409eff;
    }
  }

  // 详情模式下没有配置的数据库选项
  .no-config {
    cursor: not-allowed;
    color: #c0c4cc;
  }

  // 权限校验loading动画
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .loading-icon {
    animation: spin 1s linear infinite;
  }
}
</style>