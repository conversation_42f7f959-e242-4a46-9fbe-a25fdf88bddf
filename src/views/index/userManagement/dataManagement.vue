<script setup lang="ts" name="datamanagement">
import {onActivated, ref, onMounted} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import {
	ledgerDataAnalysis,
	ledgerDataAnalysisBatchDelete,
	getDepartmentRoleName,
	getDepartmentRoleNameActive,
} from '@/api/LedgerApi'
import {ElMessage, ElMessageBox} from 'element-plus'
import {useUserStore} from '@/stores/useUserStore'
import {USER_ROLES_ENUM} from '@/define/organization.define'

import {
	GetAllRoles,
	CreateUser,
	CheckUID,
	UpdateBatchUsers,
	CheckRemoveUser,
	DeleteBatchUsers,
	EnableBatchUser,
} from '@/api/UserApi'

const loading = ref(false)
const roles: any = ref([])
const rolesOptions: any = ref([])
const route = useRoute()
const router = useRouter()

// 表格中的操作列
const onTableClickButton = ({btn, row, index}: any) => {
	if (btn.code === 'view') {
		router.push({path: '/ledger/viewAnalysis', query: {id: row.id}})
	}

	if (btn.code === 'edit') {
		router.push({path: '/ledger/addAndEditAnalysis', query: {id: row.id}})
	}

	if (btn.code === 'delete') {
		let data: any = [row.id]
		ElMessageBox.confirm(`是否要删除 ${row.name} 数据分析?`, '消息确认', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
			.then(() => {
				ledgerDataAnalysisBatchDelete(data).then(() => {
					ElMessage.success('删除成功！')
					getList()
				})
			})
			.catch(() => {})
	}
}
// 查询条件
const runwayForm: any = ref({
	UserName: null,
	isActive: '',
	departmentName: '',
	roleName: '数据领导',
})
// 设置表格高度计算
const tableHeight = ref(0)
const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}
// 多选数据
const statisticsList: any = ref([])
const selectedCount = ref(0)
const selectionChange = (selection: []) => {
	selectedCount.value = selection.length
	statisticsList.value = selection
	console.log(statisticsList.value)
}

const addAnalysis = (e: any) => {
	if (e === 'a') {
		router.push({path: '/ledger/addAndEditAnalysis'})
	}
}

// 表中的内容
const tableData = ref([])
// 表头
const colData: any = ref([
	{
		prop: 'regionFullName',
		label: '所属区域',
		tooltip: true,
		align: 'center',
	},
	{
		prop: 'departmentFullName',
		label: '所属部门',
		align: 'center',
	},
	{
		prop: 'name',
		label: '数据管理岗名称',
		align: 'center',
	},
	{
		prop: 'userName',
		label: '账号',
		align: 'center',
	},
])
const active = ref(0)
const url = ref('')

//表格与分页关联
const reqParams = ref({
	skipCount: 0,
	maxResultCount: 10,
})
// 分页相关参数
const pagination = ref({
	total: 0,
	page: 1,
	size: 10,
})
// 查询
const getList = () => {
	loading.value = true
	getDepartmentRoleName({
		skipCount: (pagination.value.page - 1) * pagination.value.size,
		MaxResultCount: pagination.value.size,
		...runwayForm.value,
	}).then((res: any) => {
		loading.value = false
		tableData.value = res.data.items
		pagination.value.total = res.data.totalCount
	})
}
// 批量删除
const ledgerArrDelete = () => {
	let data: any = []
	statisticsList.value.forEach((item: any, index: any) => {
		data.push(item.id)
	})

	ElMessageBox.confirm('是否要批量删除数据分析?', '消息确认', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			ledgerDataAnalysisBatchDelete(data).then(() => {
				ElMessage.success('删除成功！')
				getList()
			})
		})
		.catch(() => {})
}
// 高级查询
const seniorList = () => {
	// pagination.value.page = 1
	// pagination.value.size = 10
	getList()
}
// 清空
const empty = () => {
	runwayForm.value.UserName = ''
	;(runwayForm.value.roleName = '数据领导'), (runwayForm.value.departmentName = ''), getList()
}
// 分页事件
const onPaginationChange = (val: any, type: any) => {
	if (type === 'page') {
		pagination.value.page = val
		reqParams.value.skipCount = (val - 1) * pagination.value.size
	} else {
		pagination.value.page = 1
		pagination.value.size = val
		reqParams.value.maxResultCount = pagination.value.size
	}
	getList()
}
const onTableBeforeComplete = ({items, next}: any) => {
	const temp: any = []
	if (active.value === 1) {
		items.forEach((item: any) => {
			item.details.forEach((detail: any) => {
				temp.push({
					district: item.district,
					departmentName: detail.departmentName,
					ledgerCount: detail.ledgerCount,
					ledgerDataCount: detail.ledgerDataCount,
				})
			})
		})
		next(temp)
	} else if (active.value == 2) {
		items.forEach((item: any) => {
			item.details.forEach((detail: any) => {
				temp.push({
					district: item.district,
					street: detail.street,
					cityLedgerCount: detail.cityLedgerCount,
					cityLedgerDataCount: detail.cityLedgerDataCount,
					districtLedgerCount: detail.districtLedgerCount,
					districtLedgerDataCount: detail.districtLedgerDataCount,
					historyReportCount: detail.historyReportCount,
					ledgerCount: detail.ledgerCount,
					ledgerDataCount: detail.ledgerDataCount,
					userCount: detail.userCount,
				})
			})
		})
		next(temp)
	} else {
		next(items)
	}
}
onActivated(() => {
	getList()
})

onMounted(() => {
	GetAllRoles().then((res: any) => {
		const values = Object.values(USER_ROLES_ENUM)

		// TODO: 因后端问题临时处理
		res.data.some((x: any) => {
			if (x.name === '数据领导') {
				x.name = '数据管理岗'
				return true
			}
		})

		const temp = values
			.map((val: any) => {
				const item = res.data.find((x: any) => x.name == val)
				return (
					item && {
						label: item.name,
						value: item.id,
						raw: JSON.parse(JSON.stringify(item)),
					}
				)
			})
			.filter(Boolean)
		rolesOptions.value = temp.filter((x: any) => x.label !== USER_ROLES_ENUM.YKZ)
		roles.value = temp
	})
})
const onChangeSwich = (val: any, row, id: any, falg) => {
	getDepartmentRoleNameActive([id]).then((res: any) => {
		ElMessage.success(falg ? `${row.userName}上线成功` : `${row.userName}下线成功`)
		getList()
	})
}
</script>
<template>
	<div class="dataAnalysis">
		<Block
			:enable-fixed-height="true"
			:enable-expand-content="true"
			:enableBackButton="false"
			:title="'数据管理岗管理'"
			@heightChanged="onBlockHeightChanged"
		>
			<!-- <template #topRight>
				<el-dropdown @command="addAnalysis">
					<el-button type="primary" size="small">
						新建分析<el-icon class="el-icon--right"><arrow-down /></el-icon>
					</el-button>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item command="a">空白新建</el-dropdown-item>
							<el-dropdown-item command="b">从模版新建</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
				<el-button
					type="danger"
					size="small"
					v-if="selectedCount > 0"
					@click="ledgerArrDelete"
					style="margin-left: 10px"
				>
					批量删除
				</el-button>
			</template> -->
			<template #expand>
				<div class="search-box" v-action:enter="seniorList">
					<el-input
						placeholder="请输入用户名称"
						v-model="runwayForm.UserName"
						style="width: 250px; margin-right: 10px"
					></el-input>
					<el-input
						placeholder="请输入所属部门"
						v-model="runwayForm.departmentName"
						style="width: 250px; margin-right: 10px"
					></el-input>
					<!-- <el-select
						mr-10px
						v-model="runwayForm.roleName"
						filterable
						clearable
						placeholder="请选择用户角色"
						style="width: 250px"
					>
						<el-option
							v-for="(item, index) in rolesOptions"
							:key="index"
							:label="item?.label"
							:value="item?.label"
						/>
					</el-select> -->
					<!-- <el-select
						mr-10px
						v-model="runwayForm.isActive"
						filterable
						clearable
						placeholder="请选择账号状态"
						style="width: 250px"
					>
						<el-option
							v-for="(item, index) in [
								{label: '启用', value: true},
								{label: '停用', value: false},
							]"
							:key="index"
							:label="item?.label"
							:value="item?.value"
						/>
					</el-select> -->

					<el-button size="default" type="default" @click="empty">
						<i class="i-ic-baseline-cleaning-services" mr-3px></i>
						清空
					</el-button>
					<el-button size="default" type="primary" @click="seniorList">
						<i class="i-ic-baseline-send" mr-3px></i>
						查询</el-button
					>
				</div>
			</template>
			<TableV2
				ref="tableRef"
				:loading="loading"
				:height="tableHeight"
				:columns="colData"
				:defaultTableData="tableData"
				:headers="{Urlkey: 'ledger'}"
				:enableToolbar="false"
				:enable-create="false"
				:enable-edit="false"
				:enable-delete="false"
				:enableSelection="true"
				:enableIndex="false"
				@before-complete="onTableBeforeComplete"
				:req-params="reqParams"
				:buttons="[]"
				@clickButton="onTableClickButton"
				@selection-change="selectionChange"
			>
				<template #userName="scope">
					<p>{{ scope.row?.user?.userName }}</p>
				</template>
				<template #name="scope">
					<p>{{ scope.row?.user?.name }}</p>
				</template>
				<template #__regions="scope">
					<p>{{ scope.row?.department?.region?.name }}</p>
				</template>
				<template #__department="scope">
					<p>
						{{
							`${scope.row?.department?.parent?.name || ''}${
								scope.row?.department?.parent?.name ? '-' : ''
							}${scope.row?.department?.name}`
						}}
					</p>
				</template>
				<!-- <template #isActive="scope">
					<el-switch
						v-model="scope.row.user.isActive"
						inline-prompt
						@change="
							onChangeSwich($event, scope.row.user, scope.row.user.id, scope.row.user.isActive)
						"
						style="--el-switch-on-color: var(--z-main); --el-switch-off-color: red"
						active-text="启用"
						inactive-text="停用"
					/>
				</template> -->
			</TableV2>
			<Pagination
				:total="pagination.total"
				:page-size="pagination.size"
				:current-page="pagination.page"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			>
			</Pagination>
		</Block>
	</div>
</template>
<route>
	{
		meta: {
			title: '数据管理岗管理',
		},
	}
</route>
<style scoped lang="scss">
.search-box {
	align-items: start;
	display: flex;
	padding: 10px 15px;
}
</style>
