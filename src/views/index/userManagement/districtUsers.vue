<script setup lang="ts" name="districtusers">
import {onActivated, ref, onMounted} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import {
	ledgerDataAnalysis,
	ledgerDataAnalysisBatchDelete,
	getledgerDataAnalysis,
	getLedgerStatistics,
	deleteLedgerStatisticsById,
	downloadLedgerStatistics,
	getDepartmentRoleName,
	getDepartmentRoleNameActive,
	getRegionAll,
} from '@/api/LedgerApi'
import {ElMessage, ElMessageBox} from 'element-plus'
import {useUserStore} from '@/stores/useUserStore'
import util from '@/plugin/util'
import {getNextRegions} from '@/api/OrganizeApi'

const loading = ref(false)
const route = useRoute()
const router = useRouter()
const chartType = ref([
	{name: '统计表', id: 0},
	{name: '柱状图', id: 1},
	{name: '桑葚图', id: 2},
	{name: '环形图', id: 3},
	{name: '饼图', id: 4},
	{name: '折线图', id: 5},
])
// 表格中的操作列
// const buttons: any = [
// 	{code: 'view', label: '查看', icon: 'i-majesticons-eye-line', verify: 'true'},
// 	{
// 		code: 'edit',
// 		label: '编辑',
// 		type: 'primary',
// 		icon: 'i-majesticons-pencil-alt-line',
// 		verify: 'true',
// 	},
// 	{
// 		code: 'download',
// 		label: '下载',
// 		type: 'info',
// 		icon: 'i-ic-baseline-download',
// 		verify: 'true',
// 	},
// 	{
// 		code: 'delete',
// 		label: '删除',
// 		type: 'danger',
// 		icon: 'i-ic-round-dangerous',
// 		verify: 'true',
// 	},
// ]
// 表格中的操作列
const onTableClickButton = ({btn, row, index}: any) => {
	if (btn.code === 'view') {
		router.push({path: '/ledger/viewAnalysis', query: {id: row.id}})
	}

	if (btn.code === 'edit') {
		router.push({path: '/ledger/addAndEditAnalysis', query: {id: row.id}})
	}

	if (btn.code === 'delete') {
		let data: any = [row.id]
		ElMessageBox.confirm(`是否要删除 ${row.name} 数据分析?`, '消息确认', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
			.then(() => {
				ledgerDataAnalysisBatchDelete(data).then(() => {
					ElMessage.success('删除成功！')
					getList()
				})
			})
			.catch(() => {})
	}

	// if (btn.code === 'download') {
	// 	downloadLedgerStatistics(scope.id)
	// }
}
// 查询条件
const runwayForm: any = ref({
	UserName: null,
	isActive: '',
	departmentName: '',
	roleName: '区县台账运维员',
})
const cascade = ref()
// 设置表格高度计算
const tableHeight = ref(0)
const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}
// 多选数据
const statisticsList: any = ref([])
const selectedCount = ref(0)
const selectionChange = (selection: []) => {
	selectedCount.value = selection.length
	statisticsList.value = selection
	console.log(statisticsList.value)
}
// const regionData = ref([])
// const buildTree = (items: any[]) => {
// 	const map = {} // 用于快速查找父节点的映射
// 	const tree: any[] = [] // 最终的树形结构

// 	// 遍历每个节点，并创建带有label属性的新对象
// 	items.forEach((item: {name: any; id: string | number}) => {
// 		const newNode = {...item, children: [], label: item.name} // 添加label属性
// 		map[item.id] = newNode // 将新节点放入映射中
// 	})
// 	// 遍历每个节点，如果它有父节点，则将其添加到父节点的children数组中
// 	items.forEach((item: {parentId: any; id: string | number}) => {
// 		const parentId = item.parentId
// 		if (parentId === null) {
// 			// 没有父节点，说明是根节点，直接加入树中
// 			tree.push(map[item.id])
// 		} else {
// 			// 添加到父节点的children数组中
// 			if (map[parentId]) {
// 				map[parentId].children.push(map[item.id])
// 			}
// 		}
// 	})

// 	return tree
// }
onMounted(() => {
	// getRegionAll({grade: 3}).then((res) => {
	// 	regionData.value = res.data.items
	// 	const arr = buildTree(res.data.items)

	// 	regionData.value = [{...arr[0]}]
	// })
	getRegionList()
})

// 表中的内容
const tableData = ref([])
// 表头
const colData: any = ref([
	{
		prop: 'regionFullName',
		label: '所属区域',
		tooltip: true,
		align: 'center',
	},
	{
		prop: 'departmentFullName',
		label: '所属部门',
		align: 'center',
	},
	{
		prop: 'name',
		label: '区县运维员名称',
		align: 'center',
	},
	{
		prop: 'userName',
		label: '账号',
		align: 'center',
	},

	// {
	// 	prop: 'isActive',
	// 	label: '账号状态',
	// 	align: 'center',
	// },
])
//表格与分页关联
const reqParams = ref({
	skipCount: 0,
	maxResultCount: 10,
})
// 分页相关参数
const pagination = ref({
	total: 0,
	page: 1,
	size: 10,
})
// 查询
const getList = () => {
	console.log(cascade.value?.ad)
	console.log(checkRegion.value)

	loading.value = true
	getDepartmentRoleName({
		skipCount: (pagination.value.page - 1) * pagination.value.size,
		MaxResultCount: pagination.value.size,
		...runwayForm.value,
		RegionId: checkRegion.value,
	}).then((res: any) => {
		loading.value = false
		tableData.value = res.data.items
		pagination.value.total = res.data.totalCount
	})
}

// 高级查询
const seniorList = () => {
	// console.log(pagination.value.size)
	// pagination.value.page = 1
	// pagination.value.size = 10
	getList()
}
// 清空
const empty = () => {
	runwayForm.value.UserName = ''
	runwayForm.value.departmentName = ''
	runwayForm.value.RegionId = ''
	checkRegion.value = ''
	cascade.value = {}
	getList()
}
// 分页事件
const onPaginationChange = (val: any, type: any) => {
	if (type === 'page') {
		pagination.value.page = val
		reqParams.value.skipCount = (val - 1) * pagination.value.size
	} else {
		pagination.value.page = 1
		pagination.value.size = val
		reqParams.value.maxResultCount = pagination.value.size
	}
	getList()
}

onActivated(() => {
	getList()
})
const onChangeSwich = (val: any, row, id: any, falg) => {
	getDepartmentRoleNameActive([id]).then((res: any) => {
		ElMessage.success(falg ? `${row.userName}上线成功` : `${row.userName}下线成功`)
		getList()
	})
}
const regionLsit = ref<any[]>([])
const checkRegion = ref()
async function getRegionList() {
	const res = await getNextRegions()
	const {city, district, community, street} = useUserStore().getCurrentDepartment
	console.log(useUserStore().getCurrentDepartment)
	const role = community || street || district || city
	const node = res.data.find((f: any) => f.name === role)
	console.log(node)
	const nodeChild = res.data
		.filter((f: any) => f.parentId === node?.id || f.id === node?.id)
		.map((c) => ({...c, label: c.name, value: c.id}))
	regionLsit.value = util.arrayToTree(nodeChild)
	// checkRegion.value = regionLsit.value[0].value
}
const options3 = [
	{
		prop: 'ad',
		type: 'select',
		filterable: true,
		cascadeUrl: '/api/platform/region/regions',
		// cascadeParams: {grade: 3},
		beforeInitOptions: (val: any, next: any, item: any) => {
			// const id = item.options.find((o: any) => o.value === val)?.raw.id
			// if (id) {
			// 	next.cascadeParams = {guid: id}
			// }
			// next.cascadeParams = {grade: 3}
		},
		placeholder: '请选择区域',
		options: [],
		labelWidth: 0,
	},
]
</script>
<template>
	<div class="dataAnalysis">
		<Block
			:enable-fixed-height="true"
			:enable-expand-content="true"
			:enableBackButton="false"
			:title="'区县运维员管理'"
			@heightChanged="onBlockHeightChanged"
		>
			<!-- <template #topRight>
				<el-dropdown @command="addAnalysis">
					<el-button type="primary" size="small">
						新建分析<el-icon class="el-icon--right"><arrow-down /></el-icon>
					</el-button>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item command="a">空白新建</el-dropdown-item>
							<el-dropdown-item command="b">从模版新建</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
				<el-button
					type="danger"
					size="small"
					v-if="selectedCount > 0"
					@click="ledgerArrDelete"
					style="margin-left: 10px"
				>
					批量删除
				</el-button>
			</template> -->

			<template #expand>
				<div class="search-box" v-action:enter="seniorList">
					<el-input
						placeholder="请输入用户名称"
						v-model="runwayForm.UserName"
						style="width: 250px; margin-right: 10px"
					></el-input>
					<el-input
						placeholder="请输入所属部门"
						v-model="runwayForm.departmentName"
						style="width: 250px; margin-right: 10px"
					></el-input>

					<!-- <Cascade
						style="width: 250px; margin-right: 14px; margin-top: -1px"
						v-model="cascade"
						method="get"
						:options="options3"
						:static="true"
					></Cascade> -->

					<el-tree-select
						style="width: 250px; margin-right: 14px; margin-top: -1px"
						v-model="checkRegion"
						:data="regionLsit"
						filterable
						show-checkbox
						@change="seniorList"
						check-on-click-node
						default-expand-all
						check-strictly
						collapse-tags
						placeholder="请选择所属区域"
						collapse-tags-tooltip
						:render-after-expand="false"
					></el-tree-select>

					<el-button size="default" type="default" @click="empty">
						<i class="i-ic-baseline-cleaning-services" mr-3px></i>
						清空
					</el-button>
					<el-button size="default" type="primary" @click="seniorList">
						<i class="i-ic-baseline-send" mr-3px></i>
						查询</el-button
					>
				</div>
			</template>
			<TableV2
				ref="tableRef"
				:height="tableHeight"
				:columns="colData"
				:defaultTableData="tableData"
				:headers="{Urlkey: 'ledger'}"
				:enableToolbar="false"
				:enable-create="false"
				:enable-edit="false"
				:enable-delete="false"
				:enableSelection="true"
				:enableIndex="false"
				:req-params="reqParams"
				:buttons="[]"
				@clickButton="onTableClickButton"
				@selection-change="selectionChange"
			>
				<!-- @before-complete="onTableBeforeComplete" -->
				<template #__regions="scope">
					<p>{{ scope.row?.department?.region?.name }}</p>
				</template>
				<template #name="scope">
					<p>{{ scope.row?.user?.name }}</p>
				</template>
				<template #userName="scope">
					<p>{{ scope.row?.user?.userName }}</p>
				</template>
				<template #__department="scope">
					<p>
						{{
							`${scope.row?.department?.parent?.name || ''}${
								scope.row?.department?.parent?.name ? '-' : ''
							}${scope.row?.department?.name}`
						}}
					</p>
				</template>
				<!-- <template #isActive="scope">
					<el-switch
						v-model="scope.row.user.isActive"
						inline-prompt
						@change="
							onChangeSwich($event, scope.row.user, scope.row.user.id, scope.row.user.isActive)
						"
						style="--el-switch-on-color: var(--z-main); --el-switch-off-color: red"
						active-text="启用"
						inactive-text="停用"
					/>
				</template> -->
			</TableV2>
			<Pagination
				:total="pagination.total"
				:page-size="pagination.size"
				:current-page="pagination.page"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			>
			</Pagination>
		</Block>
	</div>
</template>
<route>
	{
		meta: {
			title: '区县运维员管理',
		},
	}
</route>
<style scoped lang="scss">
.search-box {
	align-items: start;
	display: flex;
	padding: 10px 15px;
	::v-deep(.el-form-item) {
		margin: 0;
	}
}
</style>
