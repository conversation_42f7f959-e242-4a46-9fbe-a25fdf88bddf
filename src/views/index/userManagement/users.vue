<script setup lang="ts" name="users">
import {computed, nextTick, onMounted, reactive, ref} from 'vue'
import {
	GetAllRoles,
	CreateUser,
	CheckUID,
	UpdateBatchUsers,
	CheckRemoveUser,
	DeleteBatchUsers,
	EnableBatchUser,
	getDepat,
} from '@/api/UserApi'
import {USER_ROLES_ENUM} from '@/define/organization.define'
import {ElMessage, ElMessageBox} from 'element-plus'

const BUTTON_ENUM = {
	CREATE: '新增用户',
	CHANGE: '调整角色',
	DANGER: '移除用户',
}

const loading = ref(false)
const tableRef = ref()
const searchForm: any = ref({})
const formItems = reactive([
	{
		prop: 'username',
		type: 'text',
		placeholder: '输入用户名关键字筛选',
		// remoteUrl: '/api/platform/user/user-management-user-list',
		// remoteParams: {},
		// remoteLabel: 'name',
		// remoteValue: 'name',
		// remoteFilterKey: 'filter',
		// beforeRemote: (query: any, item: any) => {
		// 	if (formItems[0].remoteParams?.hasOwnProperty('departmentId')) {
		// 		return true
		// 	}
		// 	return false
		// },
	},
	{
		prop: 'role',
		type: 'select',
		placeholder: '请选择用户角色',
		options: [],
	},
	{
		prop: 'status',
		type: 'select',
		placeholder: '请选择账号状态',
		options: [
			{label: '启用', value: true},
			{label: '停用', value: false},
		],
	},
])

const dialogForm: any = ref({yzkUId: '', userole: []})
const dialogDefaultData: any = ref({})
const dialogFormItems: any = reactive([
	{
		prop: 'ykzUId',
		label: '渝快政UID',
		type: 'text',
		formShow: true,
	},
	{
		prop: 'orgName',
		label: '组织名称',
		type: 'text',
		formShow: false,
	},
	{
		prop: 'name',
		label: '用户名称',
		type: 'text',
		formShow: false,
	},
	{
		prop: 'userole',
		label: '用户角色',
		type: 'checkbox',
		options: [
			{label: USER_ROLES_ENUM.WORK_STAFF, value: USER_ROLES_ENUM.WORK_STAFF},
			{label: USER_ROLES_ENUM.DEPARTMENT_LEADER, value: USER_ROLES_ENUM.DEPARTMENT_LEADER},
			{label: '数据管理岗', value: USER_ROLES_ENUM.DATA_LEADER},
			{label: USER_ROLES_ENUM.MAIN_LEDGER, value: USER_ROLES_ENUM.MAIN_LEDGER},
			{label: USER_ROLES_ENUM.PERSON_CHARGE, value: USER_ROLES_ENUM.PERSON_CHARGE},
			{
				label: USER_ROLES_ENUM.DATA_EXPORT,
				value: USER_ROLES_ENUM.DATA_EXPORT,
				disabled: true,
			},
		],
	},
])

const tableColumns = reactive([
	{prop: 'fullDepartmentName', label: '组织名称', type: 'text'},
	{prop: 'name', label: '用户名称', type: 'text'},
	{prop: 'fullRoleName', label: '用户角色', type: 'text'},
	{prop: 'isEnable', label: '账号状态', type: 'switch'},
])
const buttons = reactive([
	{
		code: 'change',
		label: BUTTON_ENUM.CHANGE,
		type: 'primary',
	},
	{
		code: 'danger',
		label: BUTTON_ENUM.DANGER,
		type: 'danger',
	},
])

const tableHeight = ref(0)
const tableDialogTitle = ref('')
const tableDialogOpen = ref(false)
const checkout = ref(false)
const currEditRow: any = ref(null)

const treeValue: any = ref({})
const treeHeight = ref(0)
const roles: any = ref([])
const ykzRole: any = computed(() => roles.value.find((x: any) => x.label === USER_ROLES_ENUM.YKZ))

const formRef = ref()
const reqParams: any = ref({
	departmentId: JSON.parse(localStorage.getItem('currentDepartmentInfo') || '{}').id,
	skipCount: 0,
	maxResultCount: 10,
})

const pagination = ref({
	page: 1,
	size: 10,
	total: 0,
})

const treeData = ref([])
const isBatchUpdate = ref(false)

const onBlockHeightChanged = (height: any) => {
	console.log('onBlockHeightChanged', height)
	tableHeight.value = height - 75 // Block 内容高度 - 分页高度
}

const onBlockHeightChangedByTree = (height: any) => {
	console.log('onBlockHeightChangedByTree', height)
	treeHeight.value = height - 40
}

const onTableComplete = ({items, next}: any) => {
	// const temp: any = []
	// items.forEach((x: any) => {
	// 	if (x.fullRoleName.includes('数据领导') || x.fullRoleName.includes('基础功能-渝快政')) {
	// 		x.fullRoleName = x.fullRoleName
	// 			.replace('数据领导', '数据管理岗')
	// 			.replace('基础功能-渝快政,', '')
	// 			.replace('基础功能-渝快政', '')
	// 	}
	// 	temp.push(x)
	// })
	// next(temp)
}

const onTableCompleted = () => {
	pagination.value.total = tableRef.value.getTotal()
}

const onClickTableButton = ({btn, row, index}: any) => {
	console.log('onClickTableButton', btn, row, index)

	currEditRow.value = row
	dialogFormItems[1].formShow = false
	tableDialogOpen.value = true

	if (btn.code == 'change') {
		tableDialogTitle.value = BUTTON_ENUM.CHANGE
	} else if (btn.code == 'danger') {
		tableDialogTitle.value = BUTTON_ENUM.DANGER
	}

	dialogFormItems.forEach((x: any, index: number) => {
		if (btn.code === 'danger') {
			x.formShow = index !== 0
			x.disabled = true
		}

		if (btn.code === 'change') {
			x.formShow = index === 3
			x.disabled = false
		}
	})

	nextTick(() => {
		dialogDefaultData.value = {
			name: row.name,
			orgName: row.fullDepartmentName,
			userole: Array.from(new Set(row.fullRoleName.split(',') || [])),
		}
	})
}

const currentCheckDepartmentId = ref()
const onClickTreeNode = (node: any) => {
	// if (!node) return
	tableRef.value?.getElTableRef()?.clearSelection()
	console.log('onClickTreeNode', node)

	formItems[0].remoteParams = {
		departmentId: node ? node.id : '',
	}

	// reqParams.value = {
	// 	departmentId: node ? node.id : '',
	// 	skipCount: 0,
	// 	maxResultCount: 10,
	// }
	currentCheckDepartmentId.value = node ? node.id : ''
	reqParams.value.departmentId = node ? node.id : maxDpt.value.id
}

const onCreateUser = () => {
	tableDialogTitle.value = BUTTON_ENUM.CREATE
	dialogFormItems.forEach((x: any, index: number) => {
		x.formShow = index === 0 || index === 3
		x.disabled = false
	})
	tableDialogOpen.value = true
}

const onSearch = () => {
	console.log('onSearchSubmit', searchForm.value)
	const {username, role, status} = searchForm.value
	const departmentId = currentCheckDepartmentId.value
		? currentCheckDepartmentId.value
		: maxDpt.value.id
	// if (!departmentId) {
	// 	ElMessage.warning('请选择组织机构')
	// 	return
	// }
	reqParams.value = {
		filter: username,
		roleId: role,
		isEnable: status,
		departmentId,
		skipCount: 0,
		maxResultCount: pagination.value.size,
	}
}

const onPaginationChange = (val: any, type: any) => {
	if (type == 'page') {
		pagination.value.page = val
		reqParams.value.skipCount = (val - 1) * pagination.value.size
	} else {
		pagination.value.page = 1
		pagination.value.size = val
		reqParams.value.skipCount = 0
		reqParams.value.maxResultCount = pagination.value.size
	}
}

const onCheckUid = () => {
	const departmentId = treeValue.value[0]
	const ykzUId = formRef.value.getData().ykzUId
	if (!departmentId) {
		ElMessage.warning('请选择组织机构')
		return
	}

	if (!ykzUId) {
		ElMessage.warning('请输入UID')
		return
	}
	const reg = /^\d+$/
	if (!reg.test(ykzUId)) {
		ElMessage.warning('UID只能为数字')
		return
	}

	CheckUID({departmentId, ykzUId})
		.then((res: any) => {
			console.log('CheckUID', res.data)
			checkout.value = true
			ElMessage.success(res.data.message || '校验成功')
		})
		.catch((err) => {
			checkout.value = false
			window.errMsg(err, '校验UID')
		})
}

const onClickBatchUpdate = () => {
	const arr = tableRef.value.getSelectionRows()
	if (arr.length === 0) {
		ElMessage.warning('请选择用户')
		return
	}

	dialogFormItems.forEach((x: any, index: number) => {
		x.formShow = index === 3
		x.disabled = false
	})

	tableDialogTitle.value = BUTTON_ENUM.CHANGE
	tableDialogOpen.value = true
	isBatchUpdate.value = true
}

const onBatchEnable = (arr: any, enable?: boolean, fn?: Function) => {
	if (arr.length === 0) {
		ElMessage.warning('请选择用户')
		return
	}

	const str = enable ? '启用' : '停用'
	EnableBatchUser(arr)
		.then((res: any) => {
			ElMessage.success(`${str}成功`)
			nextTick(() => {
				fn && fn()
			})
		})
		.catch((err) => window.errMsg(err, str))
}

const onTableRowSwitchChange = (row: any) => {
	if (!row.providerKey) {
		ElMessage.warning('用户未绑定UID, 状态未发生变化')
		return
	}

	onBatchEnable(
		[
			{
				userId: row.id,
				isActive: row.isEnable,
			},
		],
		row.isEnable
	)
}

const onClickBatchEnable = (enable: boolean) => {
	const arr = tableRef.value.getSelectionRows()
	console.log('onClickBatchEnable', arr, enable)

	if (arr.some((x: any) => !x.providerKey)) {
		ElMessage.warning('选择用户存在未绑定UID')
		return
	}

	onBatchEnable(
		arr.map((x: any) => ({userId: x.id, isActive: enable})),
		enable,
		() => tableRef.value.reload()
	)
}

const isDataLeader = ref(false)
const onChange = (vals: any, item: any) => {
	if (vals.includes(USER_ROLES_ENUM.DATA_LEADER) && !isDataLeader.value) {
		isDataLeader.value = true
		ElMessageBox.alert(
			'你正在调整用户角色，用户设置为数据管理岗后，可管理部门业务表配置及业务表授权信息。参与部门报表分发及涉及数据管理岗审核的流程。',
			'新增数据管理岗',
			{
				confirmButtonText: '确认',
				callback: (action: any) => {},
			}
		)
	} else if (!vals.includes(USER_ROLES_ENUM.DATA_LEADER)) {
		isDataLeader.value = false
	}
}

const onSubmit = (form: any) => {
	// dialogForm.value = form
	if (treeValue.value.length === 0) {
		ElMessage.warning('请选择组织机构')
		return
	}
	// console.log(dialogForm.value)

	if (!form?.userole || form?.userole.length === 0) {
		ElMessage.warning('请选择用户角色')
		return
	}

	const replaceRole = form.userole.toString().replace('数据管理岗', '数据领导').split(',')

	const roleIds = roles.value
		.filter((x: any) => replaceRole.includes(x.label))
		.map((x: any) => x.value)
	if (isBatchUpdate.value) {
		const rows = tableRef.value.getSelectionRows()
		isBatchUpdate.value = false
		UpdateBatchUsers(
			rows.map((x: any) => ({
				userId: x.id,
				roleIds: roleIds,
				departmentId: treeValue.value[0],
			}))
		)
			.then((res: any) => {
				ElMessage.success('批量调整角色成功')
				tableDialogOpen.value = false
				tableRef.value.reload()
			})
			.catch((err) => {
				window.errMsg(err, '批量调整角色')
			})

		return
	}

	switch (tableDialogTitle.value) {
		case BUTTON_ENUM.CREATE:
			if (checkout.value) {
				CreateUser({
					ykzUId: form.ykzUId,
					roleIds: [ykzRole.value?.value],
					departmentRoleInput: {
						departmentId: treeValue.value[0],
						businessRoleIds: roleIds,
					},
				})
					.then((res: any) => {
						ElMessage.success('新增用户成功')
						tableDialogOpen.value = false
						nextTick(() => {
							tableRef.value.reload()
						})
					})
					.catch((err) => {
						window.errMsg(err, '新增用户')
					})
			} else {
				ElMessage.error('请检验UID')
			}
			break
		case BUTTON_ENUM.CHANGE:
			if (!currEditRow.value) return
			if (currEditRow.value.fullRoleName === form.userole.join(',')) {
				ElMessage.warning('用户角色未发生变化')
				return
			}

			UpdateBatchUsers([
				{
					userId: currEditRow.value.id,
					roleIds: roleIds,
					departmentId: treeValue.value[0],
				},
			])
				.then((res: any) => {
					ElMessage.success('调整角色成功')
					tableDialogOpen.value = false
					tableRef.value.reload()
				})
				.catch((err) => {
					window.errMsg(err, '调整角色')
				})
			break
		case BUTTON_ENUM.DANGER:
			if (!currEditRow.value.providerKey) {
				ElMessage.error('用户未绑定UID')
				return
			}
			const {id, providerKey} = currEditRow.value
			CheckRemoveUser({
				id,
				ykzUId: providerKey,
				departmentId: treeValue.value[0],
			})
				.then((res: any) => {
					DeleteBatchUsers({
						id,
						DepartmentId: treeValue.value[0],
						ykzUId: providerKey,
					})
						.then((res: any) => {
							ElMessage.success('移除用户成功')
							tableDialogOpen.value = false
							tableRef.value.reload()
						})
						.catch((err) => {
							window.errMsg(err, '移除用户')
						})
				})
				.catch((err) => {
					window.errMsg(err, '校验用户')
				})
			break
	}
}
const maxDpt = ref()
onMounted(() => {
	GetAllRoles().then((res: any) => {
		const values = Object.values(USER_ROLES_ENUM)

		// TODO: 因后端问题临时处理
		// res.data.some((x: any) => {
		// 	if (x.name === '数据领导') {
		// 		x.name = '数据管理岗'
		// 		return true
		// 	}
		// })

		const temp = values
			.map((val: any) => {
				const item = res.data.find((x: any) => x.name == val)
				return (
					item && {
						label: item.name,
						value: item.id,
						raw: JSON.parse(JSON.stringify(item)),
					}
				)
			})
			.filter(Boolean)
		// TODO: 因后端问题临时处理
		temp.forEach((x: any) => {
			if (x.label === USER_ROLES_ENUM.DATA_LEADER) {
				x.otherLabel = '数据管理岗'
			}
			if (x.label === USER_ROLES_ENUM.LEDGER_PERATOR) {
				x.otherLabel = '区县数据管理岗'
			}
		})
		formItems[1].options = temp.filter((x: any) => x.label !== USER_ROLES_ENUM.YKZ)
		roles.value = temp
	})
	getDepat({}).then((res) => {
		console.log(res)
		if (res) {
			maxDpt.value = res.data.items[0]
			reqParams.value.departmentId = res.data.items[0].id
		}
		// debugger
	})
})
</script>
<template>
	<div class="users">
		<div class="left">
			<Block
				title="组织机构"
				:enable-back-button="false"
				:enable-fixed-height="true"
				:enable-expand-content="false"
				:enable-close-button="false"
				@heightChanged="onBlockHeightChangedByTree"
			>
				<TreeV2
					v-model="treeValue"
					show-checkbox
					check-strictly
					url="/api/platform/department/user-management-department-list"
					:data="treeData"
					:height="treeHeight"
					:check-on-click-node="true"
					@nodeClick="onClickTreeNode"
				></TreeV2>
			</Block>
		</div>

		<div class="right">
			<Block
				title="用户列表"
				:enable-fixed-height="true"
				:enable-expand-content="true"
				:enable-back-button="false"
				@heightChanged="onBlockHeightChanged"
			>
				<template #topRight>
					<el-button size="small" type="primary" @click="onCreateUser">
						{{ BUTTON_ENUM.CREATE }}
					</el-button>
					<el-button size="small" type="primary" @click="onClickBatchUpdate"
						>批量调整</el-button
					>
					<el-button size="small" type="primary" @click="onClickBatchEnable(true)">
						批量启用
					</el-button>
					<el-button size="small" type="danger" @click="onClickBatchEnable(false)">
						批量停用
					</el-button>
				</template>

				<template #expand>
					<div class="search">
						<Form
							v-model="searchForm"
							confirm-text="查询"
							:props="formItems"
							:column-count="4"
							:enable-reset="false"
							:enable-edit="false"
							:loading="loading"
							label-width="0"
							button-vertical="flowing"
							@submit="onSearch"
						>
						</Form>
					</div>
				</template>

				<TableV2
					ref="tableRef"
					url="/api/platform/user/user-management-user-list"
					:auto-load="false"
					:req-params="reqParams"
					:columns="tableColumns"
					:buttons="buttons"
					:height="tableHeight"
					:enable-toolbar="false"
					:enable-edit="false"
					:enable-delete="false"
					:enable-selection="true"
					:pagination="pagination"
					@before-complete="onTableComplete"
					@completed="onTableCompleted"
					@click-button="onClickTableButton"
					@loading="loading = $event"
				>
					<template #fullRoleName="{row}">
						{{
							row.fullRoleName.includes('数据领导') ||
							row.fullRoleName.includes('基础功能-渝快政')
								? row.fullRoleName
										.replace('数据领导', '数据管理岗')
										.replace('基础功能-渝快政,', '')
										.replace('基础功能-渝快政', '')
								: row.fullRoleName
						}}
					</template>
					<template #isEnable="{row}">
						<el-switch v-model="row.isEnable" @change="onTableRowSwitchChange(row)" />
					</template>
				</TableV2>

				<Pagination
					:total="pagination.total"
					:page-size="pagination.size"
					:current-page="pagination.page"
					@current-change="onPaginationChange($event, 'page')"
					@size-change="onPaginationChange($event, 'size')"
				></Pagination>
			</Block>
		</div>

		<Dialog
			v-model="tableDialogOpen"
			:title="tableDialogTitle"
			:enable-button="false"
			width="600"
			class="users-index-dialog"
		>
			<Form
				ref="formRef"
				v-model="dialogForm"
				:props="dialogFormItems"
				:default-data="dialogDefaultData"
				:column-count="1"
				:enable-reset="tableDialogTitle === BUTTON_ENUM.CHANGE"
				:enable-clear="tableDialogTitle !== BUTTON_ENUM.DANGER"
				:confirm-text="tableDialogTitle === BUTTON_ENUM.DANGER ? '移除' : '提交'"
				@change="onChange"
				@submit="onSubmit"
				buttonAlign="right"
			>
				<template #form-ykzUId="scope">
					<div class="check-uid">
						<el-input v-model="scope.form.ykzUId" />
						<el-button
							v-if="tableDialogTitle === BUTTON_ENUM.CREATE"
							class="mg-left-10"
							type="primary"
							@click="onCheckUid"
						>
							检验UID
						</el-button>
					</div>
				</template>
			</Form>
		</Dialog>
	</div>
</template>
<route>
	{
		meta:{
			title: '用户管理'
		}
	}
</route>
<style scoped lang="scss">
.users {
	display: flex;

	.tip {
		align-items: center;
		display: flex;
		font-size: 12px;
		margin-left: 10px;
		opacity: 0.5;
	}

	.search {
		padding: 15px 10px 0 10px;
	}

	.left {
		flex: none;
		width: 20%;
	}

	.right {
		flex: 1;
		margin-left: 20px;
		width: calc(80% - 20px);
	}
}
</style>
<style>
.users-index-dialog {
	.check-uid {
		align-items: center;
		display: flex;
	}
}
</style>
