<script lang="ts" setup>
import {getTodoNotice} from '@/api/ReportApi'
import {ReportLedgerType, WorkTodoTypeEnum} from '@/define/statement.define'
import {router} from '@/router'
import {useUserStore} from '@/stores/useUserStore'
import {Axios} from 'axios'
import {inject, onActivated, ref} from 'vue'
import {ElNotification} from 'element-plus'

const axios = inject<Axios>('#axios')
const userStore = useUserStore()
const colData = [
	{
		title: '业务表/任务名称',
		field: 'name',
	},
	// {
	// 	title: '提交部门',
	// 	field: 'userSumbmitBigDepartment',
	// },
	{
		title: '创建部门',
		field: 'departmentName',
	},
	{
		title: '待办类型',
		field: 'reportLedgerType',
		width: '150',
	},
	{
		title: '截止时间',
		field: 'endTime',
		width: '150',
	},
]
const buttons = [
	{
		type: 'primary',
		code: 'detail',
		title: '办理',
		icon: '<i i-majesticons-eye-line></i>',
		verify: true,
	},
]
const total = ref(0)
const tableData = ref([])
const IsShowStateStop = ref(true)
const pageParams = ref({
	MaxResultCount: 10,
	SkipCount: 0,
})
const searchParams = ref({
	name: undefined,
	ReportLedgerType: undefined,
	FilterMode: 1,
})
// 表格按钮点击行为
const clickButton = (btn: {
	btn: {
		code: string
		title: string
		verify: string
	}
	scope: any
}) => {
	switch (btn.scope.reportLedgerType) {
		case WorkTodoTypeEnum.LedgerNoDataAudit:
		case WorkTodoTypeEnum.LedgerDataProgress:
			router.push({
				path: '/dataAudit/audit',
				query: {
					type: 'audit',
					ledgerId: btn.scope.ledgerId,
					auditListId: btn.scope.ledgerDataId,
					flowId: btn.scope.flowId,
				},
			})
			break
		case WorkTodoTypeEnum.ReportDistributeStart:
			router.push({
				path: '/statementTodo/detail',
				query: {id: btn.scope.planTaskId, type: 'audit', currentIndex: 1},
			})
			break
		case WorkTodoTypeEnum.ReportDistributeEnd:
			router.push({
				path: '/statementTodo/detail',
				query: {id: btn.scope.planTaskId, type: 'audit', currentIndex: 5},
			})
			break
		case WorkTodoTypeEnum.ReportFilling:
			router.push({
				path: '/statementTodo/report-task-detail',
				query: {
					reportTaskId: btn.scope.reportTaskId,
					areaOrganizationUnitId: btn.scope.areaOrganizationUnitId,
					type: 'edit',
				},
			})
			break
		case WorkTodoTypeEnum.ReportDataProgress:
			router.push({
				path: '/statementTodo/report-task-detail',
				query: {
					reportTaskId: btn.scope.reportTaskId,
					areaOrganizationUnitId: btn.scope.areaOrganizationUnitId,
					type: 'audit',
					currentIndex: 4,
				},
			})
			break
	}
}
// 分页查询
const sizeChange = (val: number) => {
	pageParams.value.MaxResultCount = val
	getInfo()
}
const currentPage = ref(1)
// 分页查询
const currentChange = (val: number) => {
	currentPage.value = val
	pageParams.value.SkipCount = pageParams.value.MaxResultCount * (val - 1)
	getInfo()
}
const getInfo = async () => {
	try {
		const res = await getTodoNotice(
			{...pageParams.value, ...searchParams.value},
			1,
			IsShowStateStop.value
		)
		tableData.value = res.data.items
		total.value = res.data.totalCount
		userStore.$patch({workCount: total.value})
	} catch (err: any) {
		window.errMsg(err, '', '当前进行任务填报人员较多，请5分钟后再试')
	}
}
const routeTo = () => {
	router.push({
		path: '/work/record',
	})
}
const tableRef = ref<any>(null)
const tableOffsetHeight = ref(-180)
const expendSearch = (height: number, expand: boolean) => {
	tableOffsetHeight.value = -(height + 170)
	tableRef.value.resize()
}
const resetSearch = () => {
	searchParams.value = {
		name: undefined,
		ReportLedgerType: undefined,
		FilterMode: 1,
	}
	getInfo()
}
onActivated(() => {
	getInfo()
})
</script>
<template>
	<div w-full h-full>
		<Block
			title="工作待办"
			:enableBackButton="false"
			:enable-expand-content="true"
			:enable-fixed-height="true"
			@content-expand="expendSearch"
		>
			<template #topRight>
				<div class="block-top-right">
					<el-checkbox
						v-model="IsShowStateStop"
						label="隐藏终止任务"
						size="small"
						@change="getInfo"
					></el-checkbox>
					<el-link
						type="primary"
						@click="routeTo"
						class="mg-left-10"
						style="font-size: 12px"
						>办理记录</el-link
					>
				</div>
			</template>
			<template #expand>
				<div class="search-box" v-action:enter="getInfo">
					<el-input
						size="default"
						v-model="searchParams.name"
						placeholder="请输入业务表/任务名称"
						class="value"
					>
					</el-input>
					<el-select
						v-model="searchParams.ReportLedgerType"
						clearable
						placeholder="请选择待办类型"
						size="default"
						@change="getInfo"
						class="value"
					>
						<el-option
							v-for="item in ReportLedgerType"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
					<el-button size="default" type="default" @click="resetSearch">
						<i class="i-ic-outline-cleaning-services" mr-3px></i>
						清空
					</el-button>
					<el-button size="default" type="primary" @click="getInfo">
						<i class="i-ic-baseline-send" mr-3px></i>
						查询</el-button
					>
				</div>
			</template>

			<BaseTableComp
				ref="tableRef"
				:offsetHeight="tableOffsetHeight"
				:colData="colData"
				:data="tableData"
				:buttons="buttons"
				:checkbox="false"
				:total="total"
				:visible-search="false"
				:visible-export="false"
				:visible-setting="false"
				:visible-header="false"
				:current-page="currentPage"
				:page-size="pageParams.MaxResultCount"
				@size-change="sizeChange"
				@current-change="currentChange"
				@click-button="clickButton"
			>
				<template #reportLedgerType="{rowData}">
					{{
						ReportLedgerType.filter((v) => v.value === rowData.reportLedgerType)[0]
							?.label
					}}
				</template>
				<template #userSumbmitBigDepartment="{rowData}">
					{{ rowData.userSumbmitBigDepartment?.name ?? '-' }}
				</template>
			</BaseTableComp>
		</Block>
	</div>
</template>
<route>
	{
		meta: {
			title:'工作待办'
		},
	}
</route>
<style lang="scss" scoped>
.search-box {
	display: flex;
	padding: 10px 15px;
	.value {
		margin-right: 10px;
		width: 250px;
	}
}

.block-top-right {
	align-items: center;
	display: flex;
}
</style>
