<script lang="ts" setup>
import {getTodoNotice} from '@/api/ReportApi'
import {ReportLedgerType, WorkTodoTypeEnum} from '@/define/statement.define'
import {router} from '@/router'
import {Axios} from 'axios'
import {inject, onActivated, ref} from 'vue'
const axios = inject<Axios>('#axios')
const colData = [
	{
		title: '业务表/报表名称',
		field: 'name',
	},
	// {
	// 	title: '提交部门',
	// 	field: 'userSumbmitBigDepartment',
	// },
	{
		title: '创建部门',
		field: 'departmentName',
	},
	{
		title: '待办类型',
		field: 'reportLedgerType',
		width: '150',
	},

	{
		title: '办理时间',
		field: 'processingTime',
		width: '150',
	},
]
const buttons = [
	{
		type: 'primary',
		code: 'detail',
		title: '查看',
		icon: '<i i-majesticons-eye-line></i>',
		verify: true,
	},
]
const total = ref(0)
const tableData = ref([])
const searchParams = ref({
	name: undefined,
	ReportLedgerType: undefined,
})
const pageParams = ref({
	MaxResultCount: 10,
	SkipCount: 0,
})
// 表格按钮点击行为
const clickButton = (btn: {
	btn: {
		code: string
		title: string
		verify: string
	}
	scope: any
}) => {
	console.log(333, btn, btn.scope)

	switch (btn.scope.reportLedgerType) {
		case WorkTodoTypeEnum.LedgerDataProgress:
			router.push({
				path: '/dataAudit/audit',
				query: {
					type: 'detail',
					ledgerId: btn.scope.ledgerId,
					auditListId: btn.scope.ledgerDataId,
					flowId: btn.scope.flowId,
				},
			})
			break
		case WorkTodoTypeEnum.ReportDistributeStart:
			router.push({
				path: '/statementTodo/detail',
				query: {id: btn.scope.planTaskId, type: 'detail'},
			})
			break
		case WorkTodoTypeEnum.ReportDistributeEnd:
			router.push({
				path: '/statementTodo/detail',
				query: {id: btn.scope.planTaskId, type: 'detail'},
			})
			break
		case WorkTodoTypeEnum.ReportFilling:
			router.push({
				path: '/statementTodo/report-task-detail',
				query: {
					reportTaskId: btn.scope.reportTaskId,
					areaOrganizationUnitId: btn.scope.areaOrganizationUnitId,
					type: 'detail',
					_from: 'workrecord',
				},
			})
			break
		case WorkTodoTypeEnum.ReportDataProgress:
			router.push({
				path: '/statementTodo/report-task-detail',
				query: {
					reportTaskId: btn.scope.reportTaskId,
					areaOrganizationUnitId: btn.scope.areaOrganizationUnitId,
					type: 'detail',
				},
			})
			break
	}
}
// 分页查询
const sizeChange = (val: number) => {
	pageParams.value.MaxResultCount = val
	getInfo()
}
const currentPage = ref(1)
// 分页查询
const currentChange = (val: number) => {
	currentPage.value = val
	pageParams.value.SkipCount = pageParams.value.MaxResultCount * (val - 1)
	getInfo()
}
const getInfo = async () => {
	const res = await getTodoNotice({...pageParams.value, ...searchParams.value}, 2, false)
	tableData.value = res.data.items
	total.value = res.data.totalCount
}
const routeTo = () => {
	router.push({
		path: '/work/record',
	})
}
const tableRef = ref<any>(null)
const tableOffsetHeight = ref(-130)
const expendSearch = (height: number, expand: boolean) => {
	tableOffsetHeight.value = -(height + 130)
	setTimeout(() => {
		tableRef.value.resize()
	}, 151)
}
const reset = () => {
	searchParams.value = {
		name: undefined,
		ReportLedgerType: undefined,
	}
	getInfo()
}
onActivated(() => {
	getInfo()
})
</script>
<template>
	<div w-full h-full>
		<Block
			title="办理记录"
			:enable-expand-content="true"
			@content-expand="expendSearch"
			:enable-fixed-height="true"
		>
			<template #expand>
				<div class="search-box" v-action:enter="getInfo">
					<el-input
						v-model="searchParams.name"
						size="default"
						placeholder="请输入业务表/报表名称"
						class="value"
					>
					</el-input>
					<el-select
						v-model="searchParams.ReportLedgerType"
						clearable
							@change="getInfo"
						placeholder="请选择待办类型"
						size="default"
						class="value"
					>
						<el-option
							v-for="item in ReportLedgerType"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>

					<el-button size="default" type="default" @click="reset">
						<i class="i-ic-outline-cleaning-services" mr-3px></i>
						清空
					</el-button>
					<el-button size="default" type="primary" @click="getInfo">
						<i class="i-ic-baseline-send" mr-3px></i>
						查询</el-button
					>
				</div>
			</template>
			<BaseTableComp
				ref="tableRef"
				:offsetHeight="tableOffsetHeight"
				:colData="colData"
				:data="tableData"
				:buttons="buttons"
				:checkbox="false"
				:total="total"
				:visible-search="false"
				:visible-export="false"
				:visible-setting="false"
				:visible-header="false"
				:current-page="currentPage"
				:page-size="pageParams.MaxResultCount"
				@size-change="sizeChange"
				@current-change="currentChange"
				@click-button="clickButton"
			>
				<template #reportLedgerType="{rowData}">
					{{
						ReportLedgerType.filter((v) => v.value === rowData.reportLedgerType)[0]
							.label
					}}
				</template>
				<template #userSumbmitBigDepartment="{rowData}">
					{{ rowData.userSumbmitBigDepartment?.name ?? '-' }}
				</template>
			</BaseTableComp>
		</Block>
	</div>
</template>
<route>
	{
		meta: {
			title:'办理记录',
		},
	}
</route>
<style lang="scss" scoped>
.search-box {
	display: flex;
	padding: 10px 15px;
	.value {
		margin-right: 10px;
		width: 250px;
	}
}
</style>
