// 表格列配置
export const columns = [
	{
		prop: 'name',
		label: '表格名称',
		slot: 'name',
	},
	{
		prop: 'relatedTasks',
		label: '关联任务数',
		slot: 'relatedTasks',
	},
	{
		prop: 'forwardTimes',
		label: '转发次数',
		width: 100,
		slot: 'forwardTimes',
	},
	{
		prop: 'recommendedWords',
		label: '推荐词',
		width: 150,
		slot: 'recommendedWords',
	},
	{
		prop: 'validationRules',
		label: '校验规则',
		width: 100,
	},
	{
		prop: 'createTime',
		label: '创建时间',
		width: 150,
	},
]
// 表格数据
export const tableData = [
	{
		id: '1',
		index: 1,
		name: '按时完成率',
		relatedTasks: [
			{
				id: '1',
				taskName: '临时任务',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '2',
				taskName: '11',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '3',
				taskName: '测试630规则',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '4',
				taskName: '060402',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '5',
				taskName: '530',
				createTime: '2025-00-29 09:09:09',
			},
		],
		forwardTimes: 22,
		recommendedWords: '',
		validationRules: '',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '2',
		index: 2,
		name: '平均延误时长',
		relatedTasks: [
			{
				id: '2',
				taskName: '11',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '3',
				taskName: '测试630规则',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '4',
				taskName: '060402',
				createTime: '2025-00-29 09:09:09',
			},
		],
		forwardTimes: 23,
		recommendedWords: '',
		validationRules: '',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '3',
		index: 3,
		name: '任务积压率',
		relatedTasks: [
			{
				id: '3',
				taskName: '测试630规则',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '4',
				taskName: '060402',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '5',
				taskName: '530',
				createTime: '2025-00-29 09:09:09',
			},
		],
		forwardTimes: 33,
		recommendedWords: '',
		validationRules: '',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '4',
		index: 4,
		name: '数据错误率',
		relatedTasks: [
			{
				id: '1',
				taskName: '临时任务',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '2',
				taskName: '11',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '3',
				taskName: '测试630规则',
				createTime: '2025-00-29 09:09:09',
			},
		],
		forwardTimes: 44,
		recommendedWords: '',
		validationRules: '',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '5',
		index: 5,
		name: '按时完成率',
		relatedTasks: [
			{
				id: '1',
				taskName: '临时任务',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '2',
				taskName: '11',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '5',
				taskName: '530',
				createTime: '2025-00-29 09:09:09',
			},
		],
		forwardTimes: 22,
		recommendedWords: '',
		validationRules: '',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '6',
		index: 6,
		name: '平均延误时长',
		relatedTasks: [
			{
				id: '2',
				taskName: '11',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '3',
				taskName: '测试630规则',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '4',
				taskName: '060402',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '5',
				taskName: '530',
				createTime: '2025-00-29 09:09:09',
			},
		],
		forwardTimes: 23,
		recommendedWords: '',
		validationRules: '',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '7',
		index: 7,
		name: '任务积压率',
		relatedTasks: [
			{
				id: '1',
				taskName: '临时任务',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '2',
				taskName: '11',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '3',
				taskName: '测试630规则',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '4',
				taskName: '060402',
				createTime: '2025-00-29 09:09:09',
			},
		],
		forwardTimes: 33,
		recommendedWords: '',
		validationRules: '',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '8',
		index: 8,
		name: '数据错误率',
		relatedTasks: [
			{
				id: '3',
				taskName: '测试630规则',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '4',
				taskName: '060402',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '5',
				taskName: '530',
				createTime: '2025-00-29 09:09:09',
			},
		],
		forwardTimes: 44,
		recommendedWords: '',
		validationRules: '',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '9',
		index: 9,
		name: '按时完成率',
		relatedTasks: [
			{
				id: '1',
				taskName: '临时任务',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '2',
				taskName: '11',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '3',
				taskName: '测试630规则',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '4',
				taskName: '060402',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '5',
				taskName: '530',
				createTime: '2025-00-29 09:09:09',
			},
		],
		forwardTimes: 22,
		recommendedWords: '',
		validationRules: '',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '10',
		index: 10,
		name: '平均延误时长',
		relatedTasks: [
			{
				id: '1',
				taskName: '临时任务',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '2',
				taskName: '11',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '3',
				taskName: '测试630规则',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '4',
				taskName: '060402',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '5',
				taskName: '530',
				createTime: '2025-00-29 09:09:09',
			},
		],
		forwardTimes: 23,
		recommendedWords: '',
		validationRules: '',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '11',
		index: 11,
		name: '任务积压率',
		relatedTasks: [
			{
				id: '1',
				taskName: '临时任务',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '2',
				taskName: '11',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '3',
				taskName: '测试630规则',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '4',
				taskName: '060402',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '5',
				taskName: '530',
				createTime: '2025-00-29 09:09:09',
			},
		],
		forwardTimes: 33,
		recommendedWords: '',
		validationRules: '',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '12',
		index: 12,
		name: '数据错误率',
		relatedTasks: [
			{
				id: '1',
				taskName: '临时任务',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '4',
				taskName: '060402',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '5',
				taskName: '530',
				createTime: '2025-00-29 09:09:09',
			},
		],
		forwardTimes: 44,
		recommendedWords: '',
		validationRules: '',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '13',
		index: 13,
		name: '按时完成率',
		relatedTasks: [
			{
				id: '1',
				taskName: '临时任务',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '2',
				taskName: '11',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '3',
				taskName: '测试630规则',
				createTime: '2025-00-29 09:09:09',
			},
			{
				id: '5',
				taskName: '530',
				createTime: '2025-00-29 09:09:09',
			},
		],
		forwardTimes: 22,
		recommendedWords: '',
		validationRules: '',
		createTime: '2025-00-29 09:09:09',
	},
]

// 关联任务列表
export const taskColumns = [
	{
		prop: 'taskName',
		label: '任务名称',
	},
	{
		prop: 'createTime',
		label: '绑定时间',
	},
	{
		prop: 'operate',
		label: '操作',
	},
]
export const connectTaskTableData = [
	{
		id: '1',
		taskName: '临时任务',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '2',
		taskName: '11',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '3',
		taskName: '测试630规则',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '4',
		taskName: '060402',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '5',
		taskName: '530',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '6',
		taskName: '530审核',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '7',
		taskName: '11111111111111111111我',
		createTime: '2025-00-29 09:09:09',
	},
]

// 绑定任务列表
export const unbindTaskTableData = [
	{
		id: '1',
		taskName: '解绑任务',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '2',
		taskName: '营救张玉荣',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '3',
		taskName: '挑战武功山',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '4',
		taskName: '徒步川西',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '5',
		taskName: '复习一整天',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '6',
		taskName: '不该休息',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '7',
		taskName: '我有罪',
		createTime: '2025-00-29 09:09:09',
	},
]

// 转发记录
export const forwardRecordColumns = [
	{
		prop: 'name',
		label: '表格名称',
	},
	{
		prop: 'forwardPerson',
		label: '转发人员',
	},
	{
		prop: 'receivePerson',
		label: '接收人',
	},
	{
		prop: 'createTime',
		label: '创建时间',
		width: 180,
	},
]
// 转发记录列表
export let forwardRecordTableData = [
	{
		id: '1',
		name: '按时完成率',
		forwardPerson: '张三丰',
		receivePerson: '胡宗昌',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '2',
		name: '平均延误时长',
		forwardPerson: '张三丰',
		receivePerson: '胡宗昌',
		createTime: '2025-00-29 09:09:09',
	},
	{
		id: '3',
		name: '任务积压率',
		forwardPerson: '张三丰',
		receivePerson: '张人生',
		createTime: '2025-00-29 09:09:09',
	},
]

