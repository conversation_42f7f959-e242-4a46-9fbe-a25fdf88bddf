<script setup lang="ts" name="DataAnalysisEvaluation">
import {useVisualizedManageStore} from '@/stores/visualizedStore'
import {useSleep} from '@/hooks/useSleep'
import {taskColumns} from '../config'
const props = defineProps({
	btnArr: {
		type: Array,
		default() {
			return [{label: '取消关联', type: 'primary', code: 'unbind'}]
		},
	},
	taskList: {
		type: Array,
		default() {
			return []
		},
	},
})
const emit = defineEmits(['update:checkRows'])
const visualizedStore = useVisualizedManageStore()

const taskTableRef = ref(null)
const taskButtons = ref<any>([])
const taskWord = ref('')
const tableData = ref<any>([])
const deepData = ref<any>([])
const isViews = ref(false)
const handlSwitch = () => {
	isViews.value = !isViews.value
}
function getList() {
	tableData.value = [...props.taskList]
	deepData.value = [...props.taskList]
}
const handlSearch = () => {
	loading.value = true
	useSleep().then(() => {
		if (!taskWord.value) {
			tableData.value = deepData.value
			loading.value = false
			return
		}
		tableData.value = tableData.value.filter((item: any) => {
			loading.value = false
			if (taskWord.value) {
				return item.taskName.includes(taskWord.value)
			}
		})
	})
}
const selectionChange = (selection: any) => {
	emit('update:checkRows', selection)
}

// 表格操作点击事件, row 当前行数据
const onTableClickButton = ({row, btn}: any) => {
	if (btn.code === 'unbind') {
	}
}

// 加载状态
const loading = ref(false)

// 初始化数据
onMounted(() => {
	taskButtons.value = [...props.btnArr]
	getList()
})
const chartRef = ref()
const selectTime = ref()
const chartOption = ref({
	title: {
		text: '关联任务趋量',
	},
	tooltip: {
		trigger: 'axis',
		axisPointer: {
			type: 'shadow',
		},
	},
	legend: {},
	grid: {
		left: '3%',
		right: '4%',
		bottom: '3%',
		containLabel: true,
	},
	xAxis: {
		type: 'value',
		boundaryGap: [0, 0.01],
	},
	yAxis: {
		type: 'category',
		data: ['2025-09-09', '2025-09-10', '2025-09-11', '2025-09-12', '2025-09-13'],
	},
	series: [
		{
			name: '2025',
			type: 'bar',
			data: [18203, 23489, 29034, 104970, 131744, 630230],
		},
	],
})
const generateRandomId = (): number => {
  return Math.floor(Math.random() * 10000)
}
const handlTime=()=>{ 
	setTimeout(()=>{
		chartOption.value.series[0].data=[generateRandomId(), generateRandomId(), generateRandomId(), generateRandomId(), generateRandomId(), generateRandomId()]
	},500)
}
</script>
<template>
	<div class="dialog_box" v-if="!isViews">
		<div class="search_box">
			<span class="name">绑定任务</span>
			<el-input
				v-model="taskWord"
				style="width: 240px; margin-right: 14px"
				placeholder="请输入任务名称"
			/>
			<el-button type="primary" @click="handlSearch">搜索</el-button>
			<el-button type="primary" @click="handlSwitch">切换视图</el-button>
		</div>
		<TableV2
			ref="taskTableRef"
			:columns="taskColumns"
			:height="400"
			:stripe="false"
			@selection-change="selectionChange"
			:enable-selection="true"
			:enable-toolbar="false"
			:enable-own-button="false"
			:defaultTableData="tableData"
			:loading="loading"
			@loading="loading = $event"
			@click-button="onTableClickButton"
		>
			<template #operate>
				<el-button type="text" size="small">取消关联</el-button>
			</template>
		</TableV2>
	</div>
	<div class="dialog_box" v-else>
		<div class="head_box">
			<el-button type="primary" @click="handlSwitch">切换视图</el-button>
			<el-date-picker
				v-model="selectTime"
				type="date"
				placeholder="请选择时间"
				format="YYYY-MM-DD"
				value-format="YYYY-MM-DD"
				@change="handlTime"
			/>
		</div>
		<Charts ref="chartRef" :option="chartOption" width="100%" height="300px"></Charts>
	</div>
</template>
<style scoped lang="scss">
.search_box {
	width: 100%;
	display: flex;
	align-items: center;
	margin-bottom: 20px;

	.name {
		margin-right: 14px;
	}
}
.head_box{
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20px;
}
</style>
