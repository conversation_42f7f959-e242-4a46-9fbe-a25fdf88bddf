<script setup lang="ts" name="DataAnalysisEvaluation">
import { usevisualizedStore, EVALUATION_NAME_OPTIONS } from '@/stores/usevisualizedStore'
import { useVisualizedManageStore } from '@/stores/visualizedStore'
import { forwardRecordColumns } from '../config'

// Store
const visualizedStore = useVisualizedManageStore()

// 搜索表单配置
const searchFormProp = ref([
    {
        label: '表格名称',
        prop: 'name',
        type: 'text',
        placeholder: '请输入表格名称',
    },
    {
        label: '转发时间',
        prop: 'datetimerange',
        type: 'datetimerange',
        placeholder: '请选择',
    },
])
const searchForm = ref({ name: '' })

// 加载状态
const loading = ref(false)
const searchLoading = ref(false)
const paginationLoading = ref(false)

// 表格ref
const tableRef = ref()
const currentRow = ref(null)

// 表格按钮配置
const buttons = [
    { label: '编辑', type: 'primary', code: 'edit' },
    { label: '删除', type: 'danger', code: 'delete', popconfirm: '确认删除吗?' },
]


// 分页
const pagination = reactive({
    page: 1,
    size: 10,
    total: 0,
})

// 列表请求参数
const reqParams = reactive({
    name: '',
    skipCount: 0,
    maxResultCount: 10,
})

// 存储当前显示的数据
const currentTableData = ref<any[]>([])

// 查询
const onSearch = async () => {
    try {
        // 设置Loading状态
        loading.value = true
        // 模拟查询延迟，增加真实性
        await new Promise((resolve) => setTimeout(resolve, 1200))
        pagination.page = 1
        reqParams.skipCount = 0
        reqParams.maxResultCount = pagination.size
        // 其他查询参数
        reqParams.name = searchForm.value.name
        getList()
        tableRef.value.reload()
    } catch (error) {
        console.error('查询失败:', error)
        ElMessage.error('查询失败，请重试')
    } finally {
        loading.value = false
    }
}

// 表格操作点击事件, row 当前行数据
const onTableClickButton = ({ row, btn, index }: any) => {
    if (btn.code === 'edit') {

    } else if (btn.code === 'delete') {
        currentTableData.value.splice(index, 1)
    }
}

// 分页事件
const onPaginationChange = async (val: any, type: any) => {
    try {
        paginationLoading.value = true

        // 模拟分页加载延迟
        await new Promise((resolve) => setTimeout(resolve, 600))

        if (type == 'page') {
            pagination.page = val
            reqParams.skipCount = (val - 1) * pagination.size
        } else {
            pagination.size = val
            reqParams.maxResultCount = pagination.size
            pagination.page = 1 // 改变每页大小时重置到第一页
        }
        getList()
        tableRef.value.reload()
    } catch (error) {
        console.error('分页操作失败:', error)
        ElMessage.error('分页操作失败，请重试')
    } finally {
        paginationLoading.value = false
    }
}
function deleteAll() {
    const data = tableRef.value?.getSelectionRows()
    if (data.length === 0) {
        ElMessage.warning('请至少选择一行数据')
        return
    }
    ElMessageBox.confirm('是否要批量删除?', '消息确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            let ids = data.map(item => item.id)
            currentTableData.value = currentTableData.value.filter(item => !ids.includes(item.id))
        })
        .catch(() => { })
}
const exportE = () => {
    const data = tableRef.value?.getSelectionRows()
    exportCSV(data)
}
const exportCSV = (data: any[]) => {
    if (data.length === 0) {
        ElMessage.warning('请至少选择一行数据')
        return
    }
    // 1. 定义表头映射（英文字段名 -> 中文显示名）
    const headerMap: any = {
        id: 'ID',
        name: '表格名称',
        forwardPerson: '转发人员',
        receivePerson: '接收人',
        createTime: '创建时间',
    }
    // 2. 获取表头行（使用自定义中文）
    const headers = Object.keys(headerMap)
        .map((key) => headerMap[key])
        .join(',')
    // 3. 处理数据行（保持与表头相同的字段顺序）
    const rows = data
        .map((item) => {
            return Object.keys(headerMap)
                .map((field) => {
                    // 处理特殊值（如日期格式化）
                    let value = item[field]
                    // 明确处理所有假值
                    if (value === null || value === undefined) {
                        value = ''
                    } else if (typeof value === 'boolean') {
                        value = value ? 'true' : 'false' // 或者直接保持 true/false
                    }
                    // 包裹在引号中并转义内容中的引号
                    return `"${String(value || '').replace(/"/g, '""')}"`
                })
                .join(',')
        })
        .join('\n')

    // 4. 组合CSV内容
    const csvContent = `${headers}\n${rows}`
    // 5. 创建下载
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `导出数据_${new Date().toISOString().slice(0, 10)}.csv`
    link.click()
    ElMessage.success(`成功导出 ${data.length} 条数据`)
}

function getList() {
    let resultData = visualizedStore.getPaginatedForwardRecordList(
        pagination.page,
        pagination.size,
        searchForm.value.name,

    )

    currentTableData.value = resultData.data || []
    pagination.total = resultData.total || 0
}

// 初始化数据
onMounted(() => {
    visualizedStore.initForwardRecordData()
    getList()
})
</script>

<template>
    <div class="container_div">
        <!-- 搜索 -->
        <div class="search">
            <Form :props="searchFormProp" v-model="searchForm" :column-count="3" :label-width="74" :enable-reset="false"
                confirm-text="查询" button-vertical="flowing" :loading="loading" @submit="onSearch"></Form>
        </div>
        <div class="oper_btns">
            <el-button type="primary" @click="deleteAll">批量删除</el-button>
            <el-button type="primary" @click="exportE">批量导出</el-button>
        </div>
        <!-- 主内容区域 - 应用搜索Loading -->
        <!-- 列表 -->
        <TableV2 ref="tableRef" :columns="forwardRecordColumns" :enable-selection="true" :req-params="reqParams"
            :enable-toolbar="false" :enable-own-button="false" :height="400" :buttons="buttons"
            :defaultTableData="currentTableData" :loading="loading || paginationLoading" @loading="loading = $event"
            @click-button="onTableClickButton">
        </TableV2>

        <!-- 分页 -->
        <Pagination :total="pagination.total" :current-page="pagination.page" :page-size="pagination.size"
            :disabled="paginationLoading || searchLoading" @current-change="onPaginationChange($event, 'page')"
            @size-change="onPaginationChange($event, 'size')"></Pagination>
    </div>

</template>
<style scoped lang="scss">
.oper_btns {
    width: 100%;
    display: flex;
    align-items: center;
    margin: 14px 0px;
}
</style>
