<script setup lang="ts" name="RecommendWords">
import {ACTION_KEY, useLocalStorage} from '@/hooks/useLocalStorage'
import {useUserStore} from '@/stores/useUserStore'
import {useSleep} from '@/hooks/useSleep'
import {s} from 'vite/dist/node/types.d-aGj9QkWt'
import {log} from 'console'
const userStore = useUserStore()
const xlsxRef = ref()
const hintTableRef = ref()
const xlConfig = ref({
	visible: false,
	enableProtect: true,
	sheets: [],
}) as any
// 是否异型表标识
const IsAlien = ref(false)
const emits = defineEmits(['update:modelValue'])
const loading = ref(false)
const dbDialogVisible = ref(false)
const storage: any = useLocalStorage()
const searchParams = ref<Form>({
	rangeName: '',
	skipCount: 0,
	maxResultCount: 10,
})
const sheets = ref<any>([])
interface Form {
	[kye: string]: any
}

interface User {
	id: number
	rangeName: string
	statisticType: string
	abscissa: number
	title: number
	status: boolean
}
const deepDataOld = ref()
const deepData = ref([
	{
		id: 1,
		rangeName: '渝中区-七星岗街道',
		statisticType: '运营员',
		abscissa: '2023-07-16',
		status: false,
		title: '运营员',
	},
	{
		id: 2,
		rangeName: '渝北区-回兴',
		statisticType: '运营员',
		abscissa: '2023-07-17',
		status: false,
		title: '运营员',
	},
	{
		id: 3,
		rangeName: '綦江区-版画院',
		statisticType: '主要领导',
		abscissa: '2023-07-18',
		status: false,
		title: '主要领导',
	},
])
const formProps = [
	{
		label: '推荐词',
		prop: 'rangeName',
		field: 'rangeName',
		type: 'text',
	},
]
watch(
	() => deepData.value.length,
	(newVal) => {
		pagination.value.total = newVal
	}
)
const columns = [
	{prop: 'rangeName', label: '推荐词', type: 'text'},
	{prop: 'statisticType', label: '绑定表格', type: 'text'},
	{prop: 'abscissa', label: '创建时间', type: 'text'},
	// {prop: 'title', label: '备注', type: 'text'},
]
const searchForm = ref({
	form: {
		rangeName: '',
		title: '',
	},
	table: [
		{
			status: false,
			title: '运营员',
		},
		{
			status: false,
			title: '主要领导',
		},
		{
			status: false,
			title: '运营员',
		},
	],
})
watch(
	() => searchForm,
	(newVal) => {
		// localStorage.setItem('searchForm_recommend', JSON.stringify(newVal))
	},
	{deep: true} // 正确写法是作为配置对象传递
)
const searchFormProp = ref([
	{
		label: '推荐词',
		prop: 'rangeName',
		type: 'text',
	},
	{
		label: '绑定表格',
		prop: 'btn',
		type: 'text',
	},
])
const columns2 = [
	{
		prop: 'title',
		label: '提示标题',
	},
	{
		prop: 'status',
		label: '是否绑定',
	},
	{
		prop: 'cz',
		label: '操作',
	},
]
// 表格中的操作列
const buttons: any = [
	{
		code: 'export',
		label: '绑定',
		type: 'text',
		verify: 'true',
	},
	// {
	// 	code: 'view',
	// 	label: '查看',
	// 	type: 'text',
	// 	verify: 'true',
	// },
	{
		code: 'edit',
		label: '编辑',
		type: 'text',
		verify: 'true',
	},
	{
		code: 'delete',
		label: '删除',
		type: 'text',
		verify: 'true',
	},
]
const handleSelectionChange = (val: User[]) => {
	const list = JSON.parse(JSON.stringify(val))
	sheets.value = list
}
const handlSearch = () => {
	loading.value = true
	useSleep().then(() => {
		if (!searchParams.value.rangeName) {
			deepData.value = deepDataOld.value
			loading.value = false
			return
		}
		deepData.value = deepDataOld.value.filter((item: User) => {
			return item.rangeName.includes(searchParams.value.rangeName)
		})
		loading.value = false
		console.log(deepData.value, deepDataOld.value)
	})
}
const onConfirm = () => {
	loading.value = true
	useSleep().then(() => {
		loading.value = false
		storage.save(ACTION_KEY.RecommendWords, deepDataOld.value)
		onClose()
	})
}
const onOpen = () => {
	loading.value = true
	useSleep().then(() => {
		if (storage.get(ACTION_KEY.RecommendWords)) {
			deepData.value = storage.get(ACTION_KEY.RecommendWords)
		}
		deepDataOld.value = deepData.value
		loading.value = false
	})
}
const onClose = () => {
	searchParams.value = {rangeName: '', skipCount: 0, maxResultCount: 10}
	emits('update:modelValue', false)
}
// 分页相关参数
const pagination = ref({
	total: deepData.value.length,
	page: 1,
	size: 10,
})
// 表格内删除按钮
const onTableClickButton = ({btn, row, index}: any) => {
	if (btn.code === 'delete') {
		ElMessageBox.confirm(`是否要删除此条数据?`, '消息确认', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
			.then(() => {
				deepDataOld.value.splice(index, 1)
				deepData.value.splice(index, 1)
			})
			.catch(() => {})
	}
	if (btn.code === 'edit') {
		openAddDialog(2, row, index)
		// dbDialogVisible.value = true
	}
	if (btn.code === 'view') {
		openAddDialog(3, row, index)
	}
	if (btn.code === 'export') {
		openAddDialog(4, row, index)
	}
}
// 分页事件
const onPaginationChange = (val: any, type: any) => {
	if (type === 'page') {
		pagination.value.page = val
		searchParams.value.skipCount = (val - 1) * pagination.value.size
	} else {
		pagination.value.page = 1
		pagination.value.size = val
		searchParams.value.maxResultCount = pagination.value.size
		searchParams.value.skipCount = 0
	}
}

// 新增\修改\回复\查看弹窗

const addDialogVisible = ref(false)
const addDialogTitle = ref('新增')
const editIndex = ref<any>(null)
const addFrom = ref<any>({
	rangeName: '',
	statisticType: '',
	abscissa: '',
	title: '',
	status: false,
})
// type 1 新增 2 修改 3 批量编辑
const openAddDialog = (type: number, row?: any, index?: number) => {
	addFrom.value = {
		rangeName: '',
		statisticType: '',
		abscissa: '',
		title: '',
		status: false,
	}
	if (type == 1) {
		addDialogTitle.value = '新增'
	}
	if (type == 2) {
		addDialogTitle.value = '编辑'
		addFrom.value = JSON.parse(JSON.stringify(row))
		editIndex.value = index
	}
	if (type == 3) {
		addDialogTitle.value = '详情'
		addFrom.value = JSON.parse(JSON.stringify(row))
		editIndex.value = index
	}
	if (type == 4) {
		addDialogTitle.value = '绑定'
		addFrom.value = JSON.parse(JSON.stringify(row))
		editIndex.value = index
	}
	addDialogVisible.value = true
}
const onAddClose = () => {
	// addFrom.value = {
	// 	rangeName: '',
	// 	statisticType: '',
	// 	abscissa: '',
	// 	title: '',
	// 	status: false,
	// }
	addDialogVisible.value = false
}
const addLoading = ref(false)
// 方法1：使用原生Date对象
const formatDate = (date = new Date()) => {
	const year = date.getFullYear()
	const month = String(date.getMonth() + 1).padStart(2, '0')
	const day = String(date.getDate()).padStart(2, '0')
	return `${year}-${month}-${day}`
}
const onAddConfirm = () => {
	if (addDialogTitle.value == '详情') return onAddClose()
	useSleep().then(() => {
		addLoading.value = false
		// addFrom.value.statisticType = userStore.userInfo.name
		if (addDialogTitle.value == '批量编辑') {
			if (sheets.value.length == 0) return ElMessage.warning('请选择要编辑的推荐词')
			const ids = sheets.value.map((item: any) => {
				return item.id
			})
			const list = deepData.value.map((item: any) => {
				if (ids.includes(item.id)) {
					return {id: item.id, ...addFrom.value}
				} else {
					return item
				}
			})
			deepData.value = list
		} else if (addDialogTitle.value == '新增') {
			// 原生深拷贝方案
			const deepClone = JSON.parse(JSON.stringify(addFrom.value))
			// deepClone.rangeName = searchForm.value.form.rangeName
			deepClone.abscissa = formatDate(new Date())
			deepData.value.forEach((d) => {
				if (d.status) {
					deepClone.statisticType = d.title
					deepData.value.push({id: deepData.value.length + 1, ...deepClone})
				}
			})
			// localStorage.setItem('searchForm_recommend', JSON.stringify(deepData.value))
		} else if (addDialogTitle.value == '编辑') {
			deepData.value[editIndex.value] = addFrom.value
		}
		onAddClose()
	})
}

// 全部删除
const allDelete = () => {
	ElMessageBox.confirm(`是否要全部删除推荐词?`, '消息确认', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			hintTableRef.value.clearSelection()
			deepDataOld.value = []
			deepData.value = []
		})
		.catch(() => {})
}
// 批量删除
const batchDelete = () => {
	if (sheets.value.length === 0) {
		return ElMessage.warning('请选择要删除的推荐词')
	}
	ElMessageBox.confirm(`是否要删除选择的推荐词?`, '消息确认', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			const ids = sheets.value.map((item: any) => {
				return item.id
			})
			const list = deepData.value.filter((item: any) => {
				return !ids.includes(item.id)
			})
			hintTableRef.value.clearSelection()
			deepData.value = list
			deepDataOld.value = list
		})
		.catch(() => {})
}
const handlTaskPromptSetting = (value: boolean) => {
	if (value) {
	} else {
	}
}
const allExport = () => {
	hintTableRef.value.clearSelection()
	deepData.value.forEach((d) => {
		d.statisticType = ''
	})
}
const batchExport = () => {
	if (sheets.value.length === 0) {
		return ElMessage.warning('请选择推荐词')
	}
	const ids = sheets.value.map((item: any) => {
		return item.id
	})
	hintTableRef.value.clearSelection()
	deepData.value.forEach((d) => {
		if (ids.includes(d.id)) {
			d.statisticType = ''
		}
	})
}
onMounted(() => {
	onOpen()
})
</script>
<template>
	<Dialog
		v-bind="$attrs"
		:enable-confirm="true"
		:auto-height="true"
		:destroy-on-close="true"
		:loading="loading"
		loading-text="正在获取数据"
		width="1000"
		@open="onOpen"
		@click-close="onClose"
		@close="onClose"
		@clickConfirm="onConfirm"
	>
		<el-form :model="searchParams" label-width="auto" :inline="true" ref="formRef">
			<el-form-item
				:label="item.label"
				v-for="(item, index) of formProps"
				:key="index"
				:prop="item.field"
			>
				<!-- 单选框 -->
				<el-radio-group
					v-if="item.type === 'radio'"
					v-model="searchParams[item.field]"
					:disabled="item.disabled"
				>
					<template v-if="item.data">
						<el-radio
							v-for="radio of item.data"
							:label="radio.value"
							:valule="radio.value"
						>
							{{ radio.label }}
						</el-radio>
					</template>
				</el-radio-group>
				<el-input
					v-else-if="item.type === 'text'"
					v-model="searchParams[item.field]"
					:placeholder="`请输入${item.label}`"
				/>
				<el-date-picker
					v-else-if="item.type === 'date'"
					v-model="searchParams[item.field]"
					type="datetime"
					:placeholder="`请选择${item.label}`"
					format="YYYY-MM-DD hh:mm:ss"
					value-format="YYYY-MM-DD hh:mm:ss"
				/>
				<!-- 下拉选择 -->
				<el-select
					v-else-if="item.type === 'select'"
					clearable
					v-model="searchParams[item.field]"
					size="default"
					style="width: 200px"
					:filterable="true"
					:placeholder="`请选择${item.label}`"
				>
					<el-option
						v-for="selectItem of item.options"
						:label="selectItem.label"
						:value="selectItem.value"
					/>
				</el-select>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="handlSearch">搜索</el-button>
			</el-form-item>
		</el-form>
		<div style="display: flex; align-items: center">
			<el-button type="primary" size="small" @click="openAddDialog(1)">新增</el-button>
			<el-dropdown class="mg-left-10 mg-right-10">
				<el-button size="small" type="primary">
					批量删除<el-icon class="el-icon--right"><arrow-down /></el-icon>
				</el-button>
				<template #dropdown>
					<el-dropdown-menu>
						<el-dropdown-item @click="allDelete">删除全部 </el-dropdown-item>
						<el-dropdown-item @click="batchDelete">删除选择 </el-dropdown-item>
					</el-dropdown-menu>
				</template>
			</el-dropdown>
			<el-dropdown class="mg-left-10 mg-right-10">
				<el-button size="small" type="primary">
					批量解绑<el-icon class="el-icon--right"><arrow-down /></el-icon>
				</el-button>
				<template #dropdown>
					<el-dropdown-menu>
						<el-dropdown-item @click="allExport">解绑全部 </el-dropdown-item>
						<el-dropdown-item @click="batchExport">解绑选择 </el-dropdown-item>
					</el-dropdown-menu>
				</template>
			</el-dropdown>
			<!-- <el-button type="primary" size="small" @click="openAddDialog(4)">批量编辑</el-button> -->
		</div>
		<!-- 表格 -->
		<TableV2
			v-model="deepData"
			:enable-selection="true"
			:auto-height="true"
			:columns="columns"
			:buttons="buttons"
			@clickButton="onTableClickButton"
			@selection-change="handleSelectionChange"
			ref="hintTableRef"
			class="mg-top-5"
		>
		</TableV2>
		<Pagination
			:total="pagination.total"
			:page-size="pagination.size"
			:current-page="pagination.page"
			@current-change="onPaginationChange($event, 'page')"
			@size-change="onPaginationChange($event, 'size')"
		>
		</Pagination>
		<Dialog
			v-model="addDialogVisible"
			:title="addDialogTitle"
			:enable-confirm="true"
			:loading="addLoading"
			:destroy-on-close="true"
			loading-text="正在获取数据"
			width="600"
			@click-close="onAddClose"
			@close="onAddClose"
			@clickConfirm="onAddConfirm"
		>
			<Form
				:props="searchFormProp"
				v-model="addFrom"
				:enable-button="false"
				:loading="loading"
				:disabled="addDialogTitle == '绑定'"
			>
				<template #form-btn>
					<el-button
						type="primary"
						size="small"
						@click="handlTaskPromptSetting(true)"
						class="mg-left-10"
					>
						批量绑定
					</el-button>
					<el-button
						type="primary"
						size="small"
						@click="handlTaskPromptSetting(false)"
						class="mg-left-10"
					>
						批量解绑
					</el-button>
				</template>
			</Form>
			<TableV2
				ref="tableRef"
				:enable-latest-data="false"
				:auto-height="true"
				v-model="deepData"
				:columns="columns2"
			>
				<template #status="{row}">
					{{ row.status ? '是' : '否' }}
				</template>
				<template #cz="{row}">
					<el-button
						v-if="!row.status"
						type="primary"
						size="small"
						@click="row.status = true"
						class="mg-left-10"
					>
						绑定表格
					</el-button>
					<el-button
						v-if="row.status"
						type="primary"
						size="small"
						@click="row.status = false"
						class="mg-left-10"
					>
						解绑表格
					</el-button>
				</template>
			</TableV2>
		</Dialog>
		<!--  -->
		<!-- <Dialog
			v-model="dbDialogVisible"
			title=""
			:enable-confirm="true"
			:destroy-on-close="true"
			width="800"
			@close="dbDialogVisible = false"
			@clickConfirm="dbDialogVisible = false"
		>
			<Form
				v-model="addFrom"
				:data="columns3"
				:grid="true"
				column-count="1"
				button-vertical="flowing"
				:enableReset="false"
				:enableClear="false"
				:enableButton="false"
				:inline="false"
				label-width="120"
			></Form>
		</Dialog> -->
	</Dialog>
</template>
<style scoped lang="scss"></style>
