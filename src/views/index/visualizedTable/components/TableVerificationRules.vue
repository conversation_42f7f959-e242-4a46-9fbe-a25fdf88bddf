<!-- 分析报告标签 -->
<template>
	<div>
		<Dialog
			width="1100"
			:loading="loading"
			height="500"
			v-bind="$attrs"
			@clickConfirm="handleConfirm"
			@close="handleConfirm"
			size="50%"
			:with-header="false"
		>
			<div class="search">
				<div class="left">
					<div>规则名称</div>
					<el-input
						style="width: 160px; margin: 0 12px"
						clearable
						v-model="state.queryParams.ruleName"
						placeholder="请输入"
					></el-input>

					<el-button type="primary" @click="handleSearch">查询</el-button>
				</div>
				<!-- 操作按钮 -->
			</div>
			<div class="btn-box">
				<el-button type="primary" @click="goAdd">新增</el-button>
				<el-dropdown style="margin: 0 12px; color: #fff" placement="bottom-start">
					<el-button type="primary"> 批量删除 </el-button>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item @click="delAll">删除全部</el-dropdown-item>
							<el-dropdown-item @click="delSelect">删除选择</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
				<el-button type="primary" :disabled="!selectionReportList.length" @click="goBind(1)"
					>批量绑定</el-button
				>
				<el-button type="primary" :disabled="!selectionReportList.length" @click="goBind(2)"
					>批量解绑</el-button
				>
			</div>

			<!-- 数据表格 -->
			<div style="padding: 0 12px">
				<el-table
					stripe
					v-loading="loading"
					default-expand-all
					ref="baseTable"
					:border="true"
					:data="tableData"
					:show-overflow-tooltip="true"
					:scrollbar-always-on="true"
					style="width: 100%; height: 340px"
					@selection-change="reportChange"
				>
					<el-table-column type="selection" width="55" />
					<el-table-column label="序号" width="60" fixed="left" align="center">
						<template #default="scope">
							{{ state.pageSize * (state.currentPage - 1) + scope.$index + 1 }}
						</template>
					</el-table-column>
					<el-table-column
						:prop="item.prop"
						align="center"
						v-for="item in tableFields"
						:label="item.label"
						:key="item.prop"
						:width="item.width"
					>
						<template #default="{row}">
							<span v-if="item.prop === 'num'" style="color: blue">{{
								row[item.prop]
							}}</span>
							<el-switch
								v-else-if="item.prop === 'status'"
								v-model="row[item.prop]"
								active-text="√"
								:disabled="true"
								inactive-text="×"
								inline-prompt
								class="custom-switch"
							/>
							<span v-else>{{ row[item.prop] }}</span>
						</template>
					</el-table-column>
					<el-table-column fixed="right" label="操作" min-width="120">
						<template #default="{row}">
							<el-button link type="primary" size="small" @click="actionData(row, 3)"
								>查看</el-button
							>
							<el-button link type="primary" size="small" @click="actionData(row, 1)"
								>编辑</el-button
							>
							<el-button link type="primary" size="small" @click="actionData(row, 2)"
								>删除</el-button
							>
						</template>
					</el-table-column>
				</el-table>
			</div>

			<div style="display: flex; justify-content: flex-end; padding: 12px">
				<el-pagination
					background
					:page-sizes="[100, 200, 300, 400]"
					layout="total, sizes, prev, pager, next, jumper"
					:total="allTableData.length"
					v-model:current-page="state.currentPage"
					:page-size="state.pageSize"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				/>
			</div>
		</Dialog>
		<!-- 新建|编辑标签-->
		<Dialog
			v-model="state.showApply"
			:loading="loading"
			:title="
				state.addType == 'add'
					? '新建表格校验规则'
					: state.addType == 'edit'
					? '编辑表格校验规则'
					: '查看表格校验规则'
			"
			width="750"
			height="600"
			@clickConfirm="saveData"
			@close="state.showApply = false"
		>
			<div style="padding-left: 20px">
				<div style="margin: 20px 0; display: flex; align-items: center">
					<div style="width: 110px; text-align: left">规则名称</div>
					<el-input
						style="width: 100%"
						clearable
						:readonly="state.addType == 'see'"
						v-model="state.reportForm.ruleName"
						placeholder="请输入"
					></el-input>
				</div>
				<div style="margin: 20px 0; display: flex; align-items: center">
					<div style="width: 110px; text-align: left">规则类型</div>
					<el-select
						:disabled="state.addType == 'see'"
						v-model="state.reportForm.ruleType"
					>
						<el-option label="必填校验" value="必填校验"></el-option>
						<el-option label="数字校验" value="数字校验"></el-option>
						<el-option label="邮箱校验" value="邮箱校验"></el-option>
						<el-option label="手机号校验校验" value="手机号校验校验"></el-option>
					</el-select>
				</div>
				<div style="margin: 20px 0; display: flex; align-items: center">
					<div style="width: 110px; text-align: left">规则描述</div>
					<el-input
						type="textarea"
						v-model="state.reportForm.remark"
						:rows="5"
						:readonly="state.addType == 'see'"
						placeholder="请输入"
						clearable
					></el-input>
				</div>
			</div>
			<div class="btn-box">
				<span>绑定表格：</span>
				<el-button
					style="margin-left: 12px"
					type="primary"
					:disabled="!selectionList.length || state.addType == 'see'"
					@click="batchAction('2')"
					>批量绑定</el-button
				>
				<el-button
					type="primary"
					:disabled="!selectionList.length || state.addType == 'see'"
					@click="batchAction('1')"
					>批量解绑</el-button
				>
			</div>

			<div style="padding-left: 120px">
				<div class="search">
					<el-input
						style="width: 160px; margin-right: 12px; margin-left: -12px"
						clearable
						placeholder="请输入表格名称"
						v-model="tableName"
					></el-input>
					<el-button type="primary" clearable @click="searchTable">搜索</el-button>
				</div>
				<el-table
					stripe
					v-loading="loading"
					default-expand-all
					ref="baseTable"
					:border="true"
					:data="bindTableData"
					:show-overflow-tooltip="true"
					:scrollbar-always-on="true"
					style="width: 100%; height: 200px"
					@selection-change="handleSelectionChange"
				>
					<el-table-column type="selection" width="55" />
					<el-table-column
						:prop="item.prop"
						align="center"
						v-for="item in bindTableFields"
						:label="item.label"
						:key="item.prop"
						:width="item.width"
					>
						<template #default="{row}">
							<span v-if="item.prop === 'status'">{{
								row[item.prop] == 1 ? '未绑定' : '已绑定'
							}}</span>
							<span v-else>{{ row[item.prop] }}</span>
						</template>
					</el-table-column>
					<el-table-column fixed="right" label="操作" min-width="120">
						<template #default="{row}">
							<el-button
								v-if="row.status == 2"
								link
								type="primary"
								size="small"
								:disabled="state.addType == 'see'"
								@click="bindActionData(row.id, '1')"
							>
								解绑
							</el-button>
							<el-button
								link
								v-if="row.status == 1"
								type="primary"
								:disabled="state.addType == 'see'"
								size="small"
								@click="bindActionData(row.id, '2')"
								>绑定表格</el-button
							>
						</template>
					</el-table-column>
				</el-table>
			</div>
		</Dialog>
	</div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted, defineExpose, nextTick} from 'vue'
import {useUserStore} from '@/stores/useUserStore'

const userInfo = useUserStore().getUserInfo
const props = defineProps<{
	selectionList: Array<any>
}>()
interface reportForm {
	ruleName: string
	isType: string
	ruleType: string
	remark: string
	[key: string]: any
}
// 表格每一项类型
interface DataItem {
	ruleName: string
	isType: string
	ruleType: string
	remark: string
	id?: number
	[key: string]: any
}

// 表格每一项类型
interface bindDataItem {
	id: number
	ruleName: string
	isType?: string
	createTime?: string
	ruleType?: string
	status: string
}
const loading = ref(false)
// 表格数据
let tableData = ref<DataItem[]>([])
// 所有数据
let allTableData = reactive<DataItem[]>([])
const state = reactive({
	showApply: false,
	showFlow: false,
	drawer: false,
	reportForm: {
		ruleName: '',
		isType: '',
		ruleType: '',
		remark: '',
		num: 0,
		bindIds: [],
		id: '',
	},
	queryParams: {
		ruleName: '',
	},
	currentPage: 1,
	pageSize: 10,
	addType: 'add',
	editId: 0,
	tableList: [],
})
const saveData = () => {
	if (state.addType == 'see') {
		state.showApply = false
	} else {
		const num = allBindTableData.value.filter(
			(item: bindDataItem) => item.status == '2'
		) as bindDataItem[]
		const bindIds = num.map((item) => item.id) || []

		if (!state.reportForm.ruleName || !state.reportForm.ruleType || !state.reportForm.remark) {
			return ElMessage.error('请完善数据')
		}
		loading.value = true
		// 新增数据
		if (state.addType == 'add') {
			const obj = {
				...state.reportForm,
				createTime: getCurrentTimeFormatted(),
				createUser: userInfo.name,
				applyStatus: '1',
				num: num.length,
				bindIds: bindIds,
				id: allTableData.length + 1,
			}
			localStorage.setItem('localTableVerRulesData', JSON.stringify([...allTableData, obj]))
		} else {
			allTableData = allTableData.map((item) =>
				item.id === state.editId
					? Object.assign({}, item, {
							...state.reportForm,
							id: state.editId,
							num: num.length,
							bindIds: bindIds,
					  })
					: item
			)

			localStorage.setItem('localTableVerRulesData', JSON.stringify([...allTableData]))
			setTimeout(() => {
				ElMessage.success('保存成功')
				updateTableData()
			}, 500)
		}
		setTimeout(() => {
			loading.value = false
			state.showApply = false
			updateTableData()
		}, 1000)
	}
}
const bindIds = ref([])
// 编辑 | 删除
const actionData = (row: any, type: number) => {
	if (type != 2) {
		state.addType = type == 1 ? 'edit' : 'see'
		for (const key in state.reportForm) {
			state.reportForm[key] = row[key]
		}

		state.editId = row.id

		bindIds.value = row.bindIds || []
		setTimeout(() => {
			state.showApply = true
			setInitData()
		}, 500)
	} else {
		ElMessageBox.confirm('确定删除数据吗？', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
			.then(async () => {
				let data = allTableData.filter((item) => item.id != row.id) || []
				tableData.value = [...data]

				localStorage.setItem('localTableVerRulesData', JSON.stringify(data))
				setTimeout(() => {
					ElMessage.success('删除成功')
					updateTableData()
				}, 500)
			})
			.catch(() => {})
	}
}

const getCurrentTimeFormatted = () => {
	const now = new Date()
	const year = now.getFullYear()
	const month = String(now.getMonth() + 1).padStart(2, '0') // 月份从0开始
	const day = String(now.getDate()).padStart(2, '0')
	const hours = String(now.getHours()).padStart(2, '0')
	const minutes = String(now.getMinutes()).padStart(2, '0')
	return `${year}-${month}-${day} ${hours}:${minutes}`
}
const tableFields = [
	{
		prop: 'ruleName',
		label: '规则名称',
		width: 160,
	},
	{
		prop: 'ruleType',
		label: '规则类型',
		width: 180,
	},
	{
		prop: 'num',
		label: '关联任务',
		width: 180,
	},
	{
		prop: 'status',
		label: '状态',
		width: 80,
	},

	{
		prop: 'createTime',
		label: '创建时间',
		width: 160,
	},
]
// 查询
const handleSearch = () => {
	if (!state.queryParams.ruleName) {
		tableData.value = [...allTableData]
	} else {
		let filteredData = allTableData.filter(
			(item) => item.ruleName.indexOf(state.queryParams.ruleName) !== -1
		)
		tableData.value = [...filteredData]
	}
}

// 展示新增
const goAdd = () => {
	state.showApply = true
	if (state.addType == 'add') {
		state.reportForm = {
			ruleName: '',
			isType: '',
			ruleType: '',
			remark: '',
			num: 0,
			bindIds: [],
			id: '',
		}
	}
	setInitData()
}
const reset = () => {
	state.queryParams = {
		ruleName: '',
	}
}
const open = () => {
	state.drawer = true
}
defineExpose({
	open,
})

// 更新数据
const updateTableData = () => {
	state.currentPage = 1
	state.pageSize = 10
	allTableData.splice(0, allTableData.length) // 清空原数组
	tableData.value = []
	const storedData = localStorage.getItem('localTableVerRulesData')
	const newData = storedData ? JSON.parse(storedData) : []
	allTableData.push(...newData)

	if (newData && newData.length) {
		tableData.value = [...allTableData]
		tableData.value.forEach((item) => {
			item.status = item.num > 0 ? true : false
		})
	} else {
		tableData.value = []
	}
}
const upData2 = () => {
	tableData.value = []
}
// 每页数量改变时触发
const handleSizeChange = (size: number) => {
	state.pageSize = size
	updateTableDataByPagination()
}

// 当前页码改变时触发
const handleCurrentChange = (page: number) => {
	state.currentPage = page
	updateTableDataByPagination()
}

// 根据分页参数更新表格数据
const updateTableDataByPagination = () => {
	const {currentPage, pageSize} = state
	const start = (currentPage - 1) * pageSize
	const end = start + pageSize

	// 截取当前页的数据
	const currentData = allTableData.slice(start, end)
	tableData.value.splice(0, tableData.value.length, ...currentData)
}
const emit = defineEmits(['close'])
const handleConfirm = () => {
	tableName.value = ''
	state.queryParams.ruleName = ''
	emit('close')
}

const tableVerRulesData = ref<reportForm>([])
const bindTableData = ref([])
const allBindTableData = ref([])
const tableName = ref('')
const searchTable = () => {
	if (!tableName.value) {
		bindTableData.value.splice(0, bindTableData.value.length, ...allBindTableData.value)
	} else {
		const filteredData = allBindTableData.value.filter((item: any) => {
			return item.tableName.indexOf(tableName.value) != -1
		})

		bindTableData.value.splice(0, bindTableData.value.length, ...filteredData)
	}
}
const selectionList = ref([])
const handleSelectionChange = (selection: any) => {
	selectionList.value = selection
}
const bindTableFields = [
	{
		prop: 'tableName',
		label: '表格名称',
		width: 160,
	},
	{
		prop: 'status',
		label: '是否绑定',
		width: 160,
	},
]
const bindActionData = (id: number, type: string) => {
	ElMessageBox.confirm(`确定${type == '2' ? '绑定表格' : '解绑'}吗？`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			updateBindTableDataItem(id, {status: type})
			setTimeout(() => {
				ElMessage.success('操作成功')
			}, 500)
		})
		.catch(() => {})
}
// 批量绑定 | 批量取消绑定
const batchAction = (type: string) => {
	ElMessageBox.confirm(`确定批量${type == '2' ? '绑定表格' : '解绑'}吗？`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const dataId = selectionList.value.map((item: any) => item.id)
			dataId.forEach((item) => {
				updateBindTableDataItem(item, {status: type})
			})
			setTimeout(() => {
				ElMessage.success('操作成功')
			}, 500)
		})
		.catch(() => {})
}
const updateBindTableDataItem = (id: number, updatedData: Partial<bindDataItem>) => {
	// 更新 bindTableData，注意使用 bindTableData.value 作为数据源
	bindTableData.value = bindTableData.value.map((item: bindDataItem) =>
		item.id === id ? Object.assign({}, item, updatedData) : item
	)
	allBindTableData.value = allBindTableData.value.map((item: bindDataItem) =>
		item.id === id ? Object.assign({}, item, updatedData) : item
	)
}

const selectionReportList = ref([])
const reportChange = (selection: any) => {
	selectionReportList.value = selection
}
const delAll = () => {
	ElMessageBox.confirm(`确定删除全部吗？`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			localStorage.removeItem('localTableVerRulesData')
			setTimeout(() => {
				ElMessage.success('操作成功')
				updateTableData()
			}, 500)
		})
		.catch(() => {})
}
const delSelect = () => {
	if (!selectionReportList.value.length) {
		return ElMessage.warning('请选择数据')
	} else {
		ElMessageBox.confirm('确定删除数据吗？', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
			.then(async () => {
				// 批量删除
				const ids = selectionReportList.value.map((item: any) => item.id)
				let data = allTableData.filter((item) => !ids.includes(item.id)) || []
				tableData.value.splice(0, tableData.value.length, ...data)
				localStorage.setItem('localTableVerRulesData', JSON.stringify(data))

				setTimeout(() => {
					ElMessage.success('删除成功')
					updateTableData()
				}, 500)
			})
			.catch(() => {})
	}
}

const setInitData = () => {
	allBindTableData.value = []
	bindTableData.value = []
	let isTable: any[] = []
	props.selectionList.forEach((item) => {
		isTable.push({
			tableName: item,
			id: isTable.length + 1,
			status:
				state.addType === 'edit' && bindIds.value.includes(isTable.length + 1) ? '2' : '1',
		})
	})
	setTimeout(() => {
		allBindTableData.value = JSON.parse(JSON.stringify(isTable))
		bindTableData.value = JSON.parse(JSON.stringify(isTable))
	}, 300)
}
const updateTableDataItem = (id: number, updatedData: any) => {
	// 更新 tableData
	tableData.value = tableData.value.map((item: any) =>
		item.id === id ? Object.assign({}, item, updatedData) : item
	)

	// 同时更新 allTableData
	allTableData = allTableData.map((item: any) =>
		item.id === id ? Object.assign({}, item, updatedData) : item
	)
}
// 批量操作
const goBind = (type: number) => {
	setInitData()
	ElMessageBox.confirm(`确定批量${type == 1 ? '绑定' : '解绑'}吗？`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const dataId = selectionReportList.value.map((item: any) => item.id)
			const bindIds = allBindTableData.value.map((item) => item.id) || []

			dataId.forEach((item) => {
				updateTableDataItem(item, {
					num: type == 1 ? bindIds.length : 0,
					bindIds: type == 1 ? bindIds : [],
				})
			})
			setTimeout(() => {
				localStorage.setItem('localTableVerRulesData', JSON.stringify(allTableData))
			}, 100)
			setTimeout(() => {
				updateTableData()
				ElMessage.success('操作成功')
			}, 300)
		})
		.catch(() => {})
}
onMounted(() => {
	state.addType = 'add'
	// setInitData()
	updateTableData()
})
</script>

<style scoped lang="scss">
:deep(.el-drawer__body) {
	padding: 12px 0 12px 0 !important;
}
.custom-switch {
	:deep(.el-switch__core) {
		--el-switch-off-color: blue !important; // 设置关闭状态背景色
		border: 1px solid #999999 !important; // 设置边框颜色

		.el-switch__inner {
			color: #ffffff; // 文字颜色
		}
	}

	:deep(.el-switch__core .is-active) {
		color: #ffffff; // 激活状态文字颜色
	}
}

.header-title {
	font-size: 16px;
	font-weight: 500;
	padding-bottom: 16px;
	padding: 12px 12px 16px 12px;
	border-bottom: 1px solid #e9e9e9;
}
.search {
	display: flex;
	align-items: center;
	border-bottom: var(--z-border);
	display: flex;
	padding: 10px;
	padding-top: 0px;
	white-space: nowrap;
	.left {
		display: flex;
		align-items: center;
	}
	span {
		font-size: 13px;
		i {
			margin-top: -1px;
		}
	}
}
.btn-box {
	display: flex;
	padding: 12px 12px 22px;
	align-items: center;
}
</style>
