<script setup lang="ts" name="DataAnalysisEvaluation">
import { useVisualizedManageStore } from '@/stores/visualizedStore'
import { taskColumns } from '../config'
const props = defineProps({
    btnArr: {
        type: Array,
        default() {
            return [
                { label: '取消关联', type: 'primary', code: 'unbind' }
            ]
        }
    }
})
const emit = defineEmits(['update:checkRows'])
const visualizedStore = useVisualizedManageStore()

const taskTableRef = ref(null)
const taskButtons = ref<any>([])
const taskWord = ref('')
const tableData = ref<any>([])

function getList() {
    visualizedStore.initUnbindTaskData()
    tableData.value = visualizedStore.searchUnbindTaskList(taskWord.value) || []
}

const selectionChange = (selection: any) => {
    emit('update:checkRows', selection)
}

// 表格操作点击事件, row 当前行数据
const onTableClickButton = ({ row, btn }: any) => {
    if (btn.code === 'unbind') {
        console.log('测试解绑任务');
    }
}

// 加载状态
const loading = ref(false)

// 初始化数据
onMounted(() => {
    taskButtons.value = [...props.btnArr]
    getList()
})

</script>
<template>
    <div class="dialog_box">
        <div class="search_box">
            <span class="name">绑定任务</span>
            <el-input v-model="taskWord" style="width: 240px;margin-right: 14px;" placeholder="请输入任务名称" />
            <el-button type="primary" @click="getList">搜索</el-button>
        </div>
        <TableV2 ref="taskTableRef" :columns="taskColumns" :height="400" :stripe="false"
            @selection-change="selectionChange" :enable-selection="true" :enable-toolbar="false"
            :enable-own-button="false" :defaultTableData="tableData" :loading="loading" @loading="loading = $event"
            @click-button="onTableClickButton">
        </TableV2>
    </div>
</template>
<style scoped lang="scss">
.search_box {
    width: 100%;
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .name {
        margin-right: 14px;
    }
}
</style>