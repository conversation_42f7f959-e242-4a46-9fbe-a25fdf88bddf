<!-- 准入规则管理 -->
<template>
	<div>
		<Dialog
			width="950"
			:loading="loading"
			height="500"
			v-model="state.drawer"
			title="准入规则管理"
			size="50%"
			:with-header="false"
		>
			<div class="search">
				<div>规则名称:</div>
				<el-input
					clearable
					style="width: 200px; margin: 0 12px"
					v-model="ruleName"
				></el-input>
				<el-button type="primary" @click="handleSearch">搜索</el-button>
			</div>
			<!-- 操作按钮 -->
			<div class="btn-box">
				<el-button type="primary" @click="goAdd">新增</el-button>
				<el-dropdown style="margin: 0 12px; color: #fff" placement="bottom-start">
					<el-button type="primary"> 批量删除 </el-button>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item @click="delAll">删除全部</el-dropdown-item>
							<el-dropdown-item @click="delSelect">删除选择</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
			</div>
			<!-- 数据表格 -->
			<div style="padding: 0 12px">
				<el-table
					stripe
					v-loading="loading"
					default-expand-all
					ref="baseTable"
					:border="true"
					:data="tableData"
					:show-overflow-tooltip="true"
					:scrollbar-always-on="true"
					style="width: 100%; height: 286px"
					@selection-change="selectionChange"
				>
					<el-table-column type="selection" width="55" />
					<!-- <el-table-column label="序号" width="60" fixed="left" align="center">
						<template #default="scope">
							{{ state.pageSize * (state.currentPage - 1) + scope.$index + 1 }}
						</template>
					</el-table-column> -->
					<el-table-column
						:prop="item.prop"
						align="center"
						v-for="item in tableFields"
						:label="item.label"
						:key="item.prop"
						:width="item.width"
					>
						<template #default="{row}">
							<span
								v-if="item.prop === 'ruleStatus'"
								:style="{color: row[item.prop] ? '#1764ce' : '#f6984d'}"
							>
								{{ row[item.prop] ? '已启用' : '已终止' }}
							</span>
							<span v-else-if="item.prop === 'applicableReports'">
								{{ row[item.prop].join() }}
							</span>

							<span v-else>{{ row[item.prop] }}</span>
						</template>
					</el-table-column>
					<el-table-column fixed="right" label="操作" min-width="120">
						<template #default="{row}">
							<el-button link size="small" type="primary" @click="seeData(row, 1)"
								>查看</el-button
							>
							<el-button link size="small" type="primary" @click="seeData(row, 2)"
								>编辑</el-button
							>
							<el-button
								link
								size="small"
								type="primary"
								@click="setStatus(row.id, !row.ruleStatus)"
								>{{ row.ruleStatus ? '终止' : '启用' }}</el-button
							>
							<el-button
								link
								type="danger"
								size="small"
								@click="actionData(row.id, 2)"
								>删除</el-button
							>
						</template>
					</el-table-column>
				</el-table>
			</div>

			<div style="display: flex; justify-content: flex-end; padding: 12px">
				<el-pagination
					background
					:page-sizes="[100, 200, 300, 400]"
					layout="total, sizes, prev, pager, next, jumper"
					:total="allTableData.length"
					v-model:current-page="state.currentPage"
					:page-size="state.pageSize"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				/>
			</div>
		</Dialog>
		<!-- 准入规则管理弹窗-->
		<Dialog
			v-model="state.showApply"
			:loading="loading"
			:title="getTitle"
			width="850"
			height="400"
			:enable-confirm="state.addType == 'see' ? false : true"
			@clickConfirm="applyConfirm"
			@close="() => (state.showApply = false)"
		>
			<Form
				ref="formRef"
				v-model="state.applyForm"
				:props="formProps"
				:rules="formRules"
				:enable-button="false"
				:disabled="state.addType == 'see' ? true : false"
				label-width="120px"
			>
				<template #form-entryCriteria="scoped">
					<el-form-item>
						<el-table
							stripe
							v-loading="loading"
							default-expand-all
							ref="baseTable"
							:border="true"
							:data="state.applyForm.entryCriteria"
							:show-overflow-tooltip="true"
							:scrollbar-always-on="true"
							style="width: 100%; max-height: 286px"
						>
							<el-table-column
								:prop="item.prop"
								align="center"
								v-for="item in addTableFields"
								:label="item.label"
								:key="item.prop"
								:width="item.width"
							>
								<template #default="{row}">
									<el-input
										v-model="row[item.prop]"
										v-if="item.prop === 'condition'"
									></el-input>
									<el-input
										v-model="row[item.prop]"
										v-else-if="item.prop === 'subCondition'"
									></el-input>
									<el-input
										v-model="row[item.prop]"
										v-else-if="item.prop === 'ruleRestrictions'"
									></el-input>
									<el-input
										v-model="row[item.prop]"
										v-else-if="item.prop === 'ruleValue'"
									></el-input>
								</template>
							</el-table-column>

							<el-table-column fixed="right" label="操作" min-width="120">
								<template #default="{row}">
									<el-button
										link
										type="danger"
										size="small"
										@click="delRow(row.id)"
										>删除</el-button
									>
								</template>
							</el-table-column>
						</el-table>
						<el-button
							type="primary"
							style="width: 100%; margin: 12px 0"
							@click="addRow"
							>新增一行</el-button
						>
					</el-form-item>
				</template>
			</Form>
		</Dialog>
	</div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted, defineExpose, computed} from 'vue'
import {useUserStore} from '@/stores/useUserStore'
const userInfo = useUserStore().getUserInfo
const formRef = ref()
const getCurrentDate = () => {
	const now = new Date()
	const year = now.getFullYear()
	const month = String(now.getMonth() + 1).padStart(2, '0') // 月份从0开始
	const day = String(now.getDate()).padStart(2, '0')
	return `${year}-${month}-${day}`
}
const options = [
	{
		label: `${getCurrentDate()}报表1`,
		value: `${getCurrentDate()}报表1`,
	},
	{
		label: `${getCurrentDate()}报表2`,
		value: `${getCurrentDate()}报表2`,
	},
]
const getTitle = computed(() => {
	return state.addType == 'add'
		? '新建准入规则'
		: state.addType == 'edit'
		? '编辑准入规则'
		: '查看准入规则'
})
const formProps = [
	{
		label: '规则名称',
		prop: 'ruleName',
		type: 'text',
		require: true,
	},
	{
		label: '规则状态',
		prop: 'ruleStatus',
		type: 'switch',
		require: true,
	},
	{
		label: '规则描述',
		prop: 'rulesRemark',
		type: 'textarea',
	},

	{
		label: '适用报表',
		prop: 'applicableReports',
		type: 'select',
		require: true,
		multiple: true,
		options,
	},
	{
		label: '规则优先级',
		prop: 'rulePriority',
		type: 'select',
		options: [
			{
				label: '高',
				value: '高',
			},
			{
				label: '中',
				value: '中',
			},
			{
				label: '低',
				value: '低',
			},
		],
	},
	{
		label: '通知方式',
		prop: 'ruleSettings',
		type: 'checkbox',
		require: true,
		options: [
			{
				label: '同部门不能重复下发同一张临时报表',
				value: '同部门不能重复下发同一张临时报表',
			},
			{
				label: '临时报表与业务表之间不能重复存在',
				value: '临时报表与业务表之间不能重复存在',
			},
			{
				label: '不涉及基层数据收集的临时报表不允许下发',
				value: '不涉及基层数据收集的临时报表不允许下发',
			},
		],
	},
	{
		label: '准入条件',
		prop: 'entryCriteria',
		require: true,
	},
]

const formRules = {
	ruleName: [{required: true, message: '请填写规则名称', trigger: 'change'}],
	ruleStatus: [{required: true, message: '请选择规则状态', trigger: 'change'}],
	applicableReports: [{required: true, message: '请选择适用报表', trigger: 'change'}],
	ruleSettings: [{required: true, message: '请选择规则设置', trigger: 'blur'}],
	entryCriteria: [{required: true, message: '请设置准入条件', trigger: 'change'}],
}

// 表格每一项类型
interface DataItem {
	ruleName: string
	ruleStatus: string
	ruleRemark: string
	createTime: string
	rulesStatus: string
	createUser: string
	id?: number
}
const loading = ref(false)

// 表格数据
let tableData = ref<DataItem[]>([])
// 所有数据
let allTableData = ref<DataItem[]>([])
const state = reactive({
	addType: 'add',
	editId: 0,
	showApply: false,
	showFlow: false,
	showSet: false,
	drawer: false,
	showExceptional: false,
	checkList: [],
	applyForm: {
		ruleName: '',
		ruleStatus: true,
		rulesRemark: '',
		applicableReports: [],
		rulePriority: '',
		ruleSettings: [],
		entryCriteria: [],
	},
	currentPage: 1,
	pageSize: 10,
})

const validateEntryCriteria = () => {
	for (const item of state.applyForm.entryCriteria) {
		const keys = Object.keys(item)
		for (const key of keys) {
			// 跳过 id 字段的验证
			if (key === 'id') continue
			// 检查字段是否为空
			if (item[key] === '' || item[key] === null || item[key] === undefined) {
				ElMessage.error('请将准入条件填写完整')
				return false
			}
		}
	}
	return true
}
const applyConfirm = () => {
	if (state.applyForm.entryCriteria.length === 0) {
		return ElMessage.error('请添加准入条件')
	}
	// 验证准入条件是否填写完整
	if (!validateEntryCriteria()) {
		return
	}
	formRef.value.validate((valid: boolean) => {
		if (valid) {
			loading.value = true

			// 新增数据
			if (state.addType == 'add') {
				const obj = {
					...state.applyForm,
					createTime: getCurrentTimeFormatted(),
					bar: 10,
					createUser: userInfo.name,
					id: allTableData.value.length + 1,
				}
				localStorage.setItem(
					'temporaryReportData',
					JSON.stringify([...allTableData.value, obj])
				)
			} else {
				allTableData.value = allTableData.value.map((item) =>
					item.id === state.editId
						? Object.assign({}, item, {...state.applyForm, id: state.editId})
						: item
				)

				localStorage.setItem('temporaryReportData', JSON.stringify([...allTableData.value]))
			}
			setTimeout(() => {
				state.showApply = false
				loading.value = false
				ElMessage.success('操作成功')
				updateTableData()
			}, 1000)
		}
	})
}

const typeOptions = [
	{
		label: '待审核',
		value: '1',
	},
	{
		label: '已取消',
		value: '2',
	},
	{
		label: '通过',
		value: '3',
	},
	{
		label: '驳回',
		value: '4',
	},
]
// 取消申请 | 删除
const actionData = (id: number, type: number) => {
	if (type == 1) {
		ElMessageBox.confirm('确定取消申请吗？', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
			.then(async () => {
				allTableData.value.forEach((item) => {
					if (item.id == id) {
						item.rulesStatus = '2'
					}
				})
				localStorage.setItem('temporaryReportData', JSON.stringify(allTableData.value))
				setTimeout(() => {
					ElMessage.success('取消申请成功')
					updateTableData()
				}, 500)
			})
			.catch(() => {
				console.log('取消申请')
			})
	} else {
		ElMessageBox.confirm('确定删除数据吗？', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
			.then(async () => {
				let data = allTableData.value.filter((item) => item.id != id) || []
				tableData.value.splice(0, tableData.value.length, ...data)
				localStorage.setItem('temporaryReportData', JSON.stringify(data))
				setTimeout(() => {
					ElMessage.success('删除成功')
					updateTableData()
				}, 500)
			})
			.catch(() => {
				console.log('取消删除')
			})
	}
}

const getCurrentTimeFormatted = () => {
	const now = new Date()
	const year = now.getFullYear()
	const month = String(now.getMonth() + 1).padStart(2, '0') // 月份从0开始
	const day = String(now.getDate()).padStart(2, '0')
	const hours = String(now.getHours()).padStart(2, '0')
	const minutes = String(now.getMinutes()).padStart(2, '0')
	const seconds = String(now.getSeconds()).padStart(2, '0')
	return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}
const tableFields = [
	{
		prop: 'ruleName',
		label: '规则名称',
		width: 156,
	},
	{
		prop: 'applicableReports',
		label: '适用报表',
		width: 156,
	},
	{
		prop: 'createTime',
		label: '创建时间',
		width: 156,
	},

	{
		prop: 'ruleStatus',
		label: '状态',
		width: 90,
	},
]
const addTableFields = [
	{
		prop: 'condition',
		label: '条件',
		width: 156,
	},
	{
		prop: 'subCondition',
		label: '子条件',
		width: 156,
	},
	{
		prop: 'ruleRestrictions',
		label: '规则限制',
		width: 156,
	},

	{
		prop: 'ruleValue',
		label: '规则值',
		width: 90,
	},
]
const ruleName = ref('')
const handleSearch = () => {
	if (!ruleName.value) {
		tableData.value.splice(0, tableData.value.length, ...allTableData.value)
	} else {
		const filteredData = allTableData.value.filter((item) => item.ruleName === ruleName.value)
		tableData.value.splice(0, tableData.value.length, ...filteredData)
	}
}
// 展示新增
const goAdd = () => {
	state.addType = 'add'
	state.showApply = true
	formRef.value.clear()

	state.applyForm = {
		ruleName: '',
		ruleStatus: true,
		rulesRemark: '',
		applicableReports: [],
		rulePriority: '',
		ruleSettings: [],
		entryCriteria: [],
	}
}
const conditionList = [
	{
		label: '用户角色',
		value: 1,
	},
	{
		label: '部门',
		value: 2,
	},
	{
		label: '职位',
		value: 3,
	},
	{
		label: '地区',
		value: 4,
	},
]
const subConditionList = [
	{
		label: '财务部',
		value: 1,
	},
	{
		label: '部门',
		value: 2,
	},
	{
		label: '职位',
		value: 3,
	},
	{
		label: '地区',
		value: 4,
	},
]
const ruleRestrictionsList = [
	{
		label: '<',
		value: '<',
	},
	{
		label: '>',
		value: '>',
	},
	{
		label: '=',
		value: '=',
	},
	{
		label: '≤',
		value: '≤',
	},
	{
		label: '≥',
		value: '≥',
	},
]

// 更新数据
const updateTableData = () => {
	state.currentPage = 1
	allTableData.value.splice(0, allTableData.value.length) // 清空原数组
	const newData = JSON.parse(localStorage.getItem('temporaryReportData')) || []
	allTableData.value.push(...newData)

	if (newData && newData.length) {
		tableData.value.splice(0, tableData.value.length, ...newData.slice(0, state.pageSize))
	} else {
		tableData.value.splice(0, tableData.value.length)
	}
}

// 每页数量改变时触发
const handleSizeChange = (size: number) => {
	state.pageSize = size
	updateTableDataByPagination()
}

// 当前页码改变时触发
const handleCurrentChange = (page: number) => {
	state.currentPage = page
	updateTableDataByPagination()
}

// 根据分页参数更新表格数据
const updateTableDataByPagination = () => {
	const {currentPage, pageSize} = state
	const start = (currentPage - 1) * pageSize
	const end = start + pageSize

	// 截取当前页的数据
	const currentData = allTableData.value.slice(start, end)
	tableData.value.splice(0, tableData.value.length, ...currentData)
}

const selectionReportList = ref([])
const selectionChange = (selection: any) => {
	selectionReportList.value = selection
}
const delAll = () => {
	ElMessageBox.confirm(`确定删除全部吗？`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			localStorage.removeItem('temporaryReportData')
			setTimeout(() => {
				ElMessage.success('操作成功')
				updateTableData()
			}, 500)
		})
		.catch(() => {})
}
const delSelect = () => {
	if (!selectionReportList.value.length) {
		return ElMessage.warning('请选择数据')
	} else {
		ElMessageBox.confirm('确定删除数据吗？', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
			.then(async () => {
				// 批量删除
				const ids = selectionReportList.value.map((item: any) => item.id)
				let data = allTableData.value.filter((item) => !ids.includes(item.id)) || []
				tableData.value.splice(0, tableData.value.length, ...data)
				localStorage.setItem('temporaryReportData', JSON.stringify(data))

				setTimeout(() => {
					ElMessage.success('删除成功')
					updateTableData()
				}, 500)
			})
			.catch(() => {})
	}
}
const addRow = () => {
	state.applyForm.entryCriteria.push({
		id: state.applyForm.entryCriteria.length + 1,
		condition: '',
		subCondition: '',
		ruleRestrictions: '',
		ruleValue: '',
	})
}
const seeData = (row: any, type: number) => {
	for (const key in state.applyForm) {
		state.applyForm[key] = row[key]
	}
	state.addType = type == 1 ? 'see' : 'edit'
	state.editId = row.id
	state.showApply = true
}
const setStatus = (id: number, status: boolean) => {
	ElMessageBox.confirm(`确定${status ? '启用' : '终止'}数据吗？`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			tableData.value.forEach((item) => {
				if (item.id == id) {
					item.ruleStatus = status
				}
			})
			allTableData.value.forEach((item) => {
				if (item.id == id) {
					item.ruleStatus = status
				}
			})

			localStorage.setItem('temporaryReportData', JSON.stringify(allTableData.value))
			setTimeout(() => {
				ElMessage.success('操作成功')
				updateTableData()
			}, 500)
		})
		.catch(() => {})
}
const delRow = (id: number) => {
	state.applyForm.entryCriteria = state.applyForm.entryCriteria.filter((item) => item.id !== id)
}
const open = () => {
	state.drawer = true
}
defineExpose({
	open,
})
onMounted(() => {
	updateTableData()
})
</script>

<style scoped lang="scss">
:deep(.el-drawer__body) {
	padding: 12px 0 12px 0 !important;
}

.header-title {
	font-size: 16px;
	font-weight: 500;
	padding-bottom: 16px;
	padding: 12px 12px 16px 12px;
	border-bottom: 1px solid #e9e9e9;
}
.search {
	align-items: center;
	border-bottom: var(--z-border);
	display: flex;
	padding: 10px;
	white-space: nowrap;

	span {
		font-size: 13px;
		i {
			margin-top: -1px;
		}
	}
}
.btn-box {
	display: flex;
	padding: 12px 12px 22px;
}
</style>
