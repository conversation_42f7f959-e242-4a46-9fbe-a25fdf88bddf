<script setup lang="ts" name="CityMechanism">
import {ACTION_KEY, useLocalStorage} from '@/hooks/useLocalStorage'
import {useSleep} from '@/hooks/useSleep'
import {useUserStore} from '@/stores/useUserStore'
const userInfo = useUserStore().getUserInfo
const emits = defineEmits(['update:modelValue'])
const loading = ref(false)
const storage: any = useLocalStorage()
const onConfirm = () => {
	loading.value = true
	useSleep().then(() => {
		loading.value = false
		onClose()
	})
}
const onOpen = () => {
	loading.value = true
	useSleep().then(() => {
		loading.value = false
	})
}
const onClose = () => {
	emits('update:modelValue', false)
}
</script>
<template>
	<Dialog
		v-bind="$attrs"
		:enable-confirm="true"
		:destroy-on-close="true"
		:loading="loading"
		loading-text="正在获取数据"
		width="1000"
		@open="onOpen"
		@click-close="onClose"
		@close="onClose"
		@clickConfirm="onConfirm"
	>
		<div style="height: 400px">
			<div style="display: flex; align-items: center; line-height: 40px;">
				<el-icon size="30" color="#1764CE"><Share /></el-icon>
				<div style="color: #000; font-size: 16px; font-weight: bold;margin-left: 10px;">准入流程说明</div>
			</div>
			<el-steps direction="vertical" :active="-1">
				<el-step>
					<template #icon>
						<el-icon size="30" color="#1764CE"><UserFilled /></el-icon>
					</template>
					<template #description>
						<div>
							<div style="color: #000; font-size: 16px;">1.工作人员创建报表</div>
							<div style="color: #666; font-size: 16px;line-height: 30px;">工作人员根据部门业务需求，收集需要采集的数据内容，按需创建相应的临时报表。</div>
						</div>
					</template>
				</el-step>
				<el-step>
					<template #icon>
						<el-icon size="30" color="#1764CE"><Avatar /></el-icon>
					</template>
					<template #description>
						<div>
							<div style="color: #000; font-size: 16px;">2.分管领导审核</div>
							<div style="color: #666; font-size: 16px;line-height: 30px;">分管领导对工作人员创建成功并提交准入审核的临时报表内容进行确认。如需对外采集数据，则分管领导审核完成后发起下一步审核工作。</div>
						</div>
					</template>
				</el-step>
				<el-step>
					<template #icon>
						<el-icon size="30" color="#1764CE"><UserFilled /></el-icon>
					</template>
					<template #description>
						<div>
							<div style="color: #000; font-size: 16px;">3.数据管理岗最终审核</div>
							<div style="color: #666; font-size: 16px;line-height: 30px;">数据管理岗对部门所有临时报表准入下发基层进行数据收集的最终审核，需对临时报表中内容进行确认，包括：</div>
							<ul style="padding: 0 20px;">
								<li style="color: #666; font-size: 14px;">临时报表数据采集需求是否合理</li>
								<li style="color: #666; font-size: 14px;">临时报表中数据项字段是否必须</li>
								<li style="color: #666; font-size: 14px;">数据填报部门设置是否合理</li>
								<li style="color: #666; font-size: 14px;">数据归集流程是否合理</li>
							</ul>
						</div>
					</template>
				</el-step>
				<el-step>
					<template #icon>
						<el-icon size="30" color="#1764CE"><List /></el-icon>
					</template>
					<template #description>
						<div>
							<div style="color: #000; font-size: 16px;">4.数据填充与归集</div>
							<div style="color: #666; font-size: 16px;line-height: 30px;">审核通过后工作人员基于创建的临时报表从已有业务数据或基础数据中关联对应数据进行自动填充，完成数据归集。</div>
						</div>
					</template>
				</el-step>
			</el-steps>
		</div>
	</Dialog>
</template>
<style scoped lang="scss">
::v-deep ul{
	list-style: disc;
}
</style>
