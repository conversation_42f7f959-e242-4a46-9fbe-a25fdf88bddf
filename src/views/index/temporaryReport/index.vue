<script setup lang="ts" name="temporaryreport">
import {FlowAuditTypes, FlowAuditTypeData} from '@/define/Workflow'
import {auditSomeApi, auditAllApi} from '@/api/ReportApi'
import AddRules from './components/AddRules.vue'
import CityMechanism from './components/CityMechanism.vue'
const router = useRouter()

const formProps = ref([{label: '任务名称', prop: 'title', type: 'text'}])
const form = ref({title: ''})
const addRulesRef = ref(false)
const statusArr = [
	{name: '激活', color: '#3D7FFF', state: 1},
	{name: '待激活', color: '#5DC1AA', state: 2},
	{name: '完成', color: '#FD6B69', state: 3},
	{name: '关闭', color: '#EA8B60', state: 4},
	{name: '加签状态', color: '#3D7FFF', state: 1},
	{name: '转移给其他人', color: '#5DC1AA', state: 2},
	{name: '作废', color: '#FD6B69', state: 3},
	{name: '子流程运行中', color: '#EA8B60', state: 4},
]

const statusArr2 = [
	{name: '-', color: '#303030', state: 0},
	{name: '已通过', color: '#4caf50', state: 1},
	{name: '已驳回', color: '#FD6B69', state: 2},
]

const columns = [
	{label: '任务名称', prop: 'title', type: 'text'},
	{label: '任务类型', prop: '_category', type: 'text'},
	{label: '提交部门', prop: 'createUserLargeDepartmentName', type: 'text'},
	{label: '提交科室', prop: 'createUserDepartmentName', type: 'text'},
	{label: '提交人', prop: 'createUserName', type: 'text'},
	{label: '提交时间', prop: 'businessRelevanceTime', type: 'datetime', sortable: 'custom'},
	{label: '截止时间', prop: 'deadline', type: 'datetime'},
]
const tableRef = ref()
const tableHeight = ref(0)
const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})

const reqParams = reactive({
	title: '',
	skipCount: 0,
	maxResultCount: 10,
	businessType: FlowAuditTypes.ReportTaskIssude,
})

const showAuditDialog = ref(false)
const isBatchAuditing = ref(false)
const auditFormRef = ref()
const auditForm = ref({code: '', des: ''})

const showTodoRemind = ref(false)
const tableRefRecord = ref()
const recordColumns = [
	{label: '任务名称', prop: 'title', type: 'text'},
	// {label: '提交人', prop: 'createUserName', type: 'text'},
	{label: '提交人部门', prop: 'createUserLargeDepartmentName', type: 'text'},
	{label: '提交人科室', prop: 'createUserDepartmentName', type: 'text'},
	{label: '提交时间', prop: 'creationTime', type: 'datetime'},
	{
		label: '办理时间',
		prop: 'processTime',
		type: 'datetime',
		sortable: 'custom',
	},
	{label: '审核结果', prop: 'outcome', type: 'switch'},
]
const reqParamsRecord = reactive({
	skipCount: 0,
	maxResultCount: 10,
	Sorting: `processTime desc`,
})
const paginationRecord = reactive({
	page: 1,
	size: 10,
	total: 0,
})

const onPaginationChange = (val: any, type: any) => {
	if (type == 'page') {
		pagination.page = val
		reqParams.skipCount = (val - 1) * pagination.size
	} else {
		pagination.size = val
		reqParams.maxResultCount = pagination.size
	}
}

const onPaginationRecordChange = (val: any, type: any) => {
	if (type == 'page') {
		paginationRecord.page = val
		reqParamsRecord.skipCount = (val - 1) * paginationRecord.size
	} else {
		paginationRecord.size = val
		reqParamsRecord.maxResultCount = paginationRecord.size
	}
}

const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}

const beforeTableComplete = ({items, next}: any) => {
	const temp: any = []
	items.forEach((x: any) => {
		x.title = x.process.title
		x.createUserName = x.process.createUserName
		x.createUserLargeDepartmentName = x.process.createUserLargeDepartmentName
		x.createUserDepartmentName = x.process.createUserDepartmentName
		x.ledgerName = x.process.businessExtend ? x.process.businessExtend.LedgerName : ''
		x.businessRelevanceTime = x.process.businessRelevanceTime
		x.creationTime = x.process.creationTime
		x.deadline = x.process.deadline ?? '-'
		x.state = x.state
		temp.push(x)
	})
	next(temp)
}

const beforeRecordComplete = ({items, next}: any) => {
	const temp: any = []
	items.forEach((x: any) => {
		x.title = x.process.title
		// x.ledgerName = x.process.businessExtend ? x.process.businessExtend.LedgerName : ''
		x.createUserName = x.process.createUserName
		x.createUserDepartmentName = x.process.createUserDepartmentName
		x.createUserLargeDepartmentName = x.process.createUserLargeDepartmentName
		x.creationTime = x.process.creationTime
		;(x.finishTime = x.process.finishTime ?? '-'), (x.outcome = x.state == 3 ? 1 : 2)
		temp.push(x)
	})
	next(temp)
}

const onSearch = () => {
	pagination.page = 1
	reqParams.skipCount = 0
	reqParams.title = form.value.title
}

const onBatchAudit = (type: number) => {
	isBatchAuditing.value = !!type

	if (isBatchAuditing.value) {
		const rows = tableRef.value.getSelectionRows()
		if (rows.length === 0) {
			ElMessage.error('请选择要审核的任务')
			return
		}
	}

	showAuditDialog.value = true
	auditForm.value = {code: '', des: ''}
}

const onAuditConfirm = () => {
	auditFormRef.value.validate((valid: any) => {
		if (valid) {
			const ids = tableRef.value.getSelectionRows().map((x: any) => x.id)
			if (isBatchAuditing.value) {
				auditSomeApi({
					code: auditForm.value.code,
					des: auditForm.value.des,
					taskIds: ids,
				}).then(() => {
					ElMessage.success('批量审核成功')
					showAuditDialog.value = false
					auditForm.value = {code: '', des: ''}
					tableRef.value.clearSelection()
					tableRef.value.reload()
				})
			} else {
				auditAllApi({
					code: auditForm.value.code,
					des: auditForm.value.des,
				}).then(() => {
					ElMessage.success('全部审核成功')
					showAuditDialog.value = false
					auditForm.value = {code: '', des: ''}
					tableRef.value.clearSelection()
					tableRef.value.reload()
				})
			}
		}
	})
}

const onTableButtonClick = ({btn, row}: any) => {
	if (btn.code === 'view') {
		showTodoRemind.value = false
		if (row.process.businessType === FlowAuditTypes.ReportTaskIssude) {
			// 下发
			router.push({
				path: '/taskPending/detail',
				query: {
					id: row.process.businessId,
					taskId: row.id,
					type: 'detail',
					currentIndex: 1,
				},
			})
		} else if (
			row.process.businessType === FlowAuditTypes.ReportTaskDataAudit ||
			row.process.businessType === FlowAuditTypes.ReportTaskTranspondAudit
			// 数据审核转发
		) {
			router.push({
				path: '/taskPending/report-task-detail',
				query: {
					taskId: row.id,
					reportTaskId: row.process.businessExtend.ReportTaskId,
					areaOrganizationUnitId: row.process.businessExtend.AreaOrganizationUnitId,
					id: row.process.businessExtend.Id,
					type: 'detail',
					currentIndex: 4,
					businessType: row.process.businessType,
				},
			})
		} else if (row.process.businessType === FlowAuditTypes.LedgerDataExport) {
			router.push({
				path: '/taskPending/export-audit',
				query: {
					id: row.process.businessId,
					rid: row.processId,
					tid: row.id,
				},
			})
		} else {
			router.push({
				path: '/taskPending/task-review-details',
				query: {
					id: row.id,
					taskId: row.process.id,
					keyword: row.process.keyword2,
					businessId: row.process.businessId,
					Remark: row.process.businessExtend.Remark,
					showButton: 'false',
				},
			})
		}
	} else if (btn.code === 'toexamine') {
		if (row.process.businessType === FlowAuditTypes.ReportTaskIssude) {
			router.push({
				path: '/taskPending/detail',
				query: {
					id: row.process.businessId,
					taskId: row.id,
					type: 'audit',
					currentIndex: 1,
				},
			})
		} else if (
			row.process.businessType === FlowAuditTypes.ReportTaskDataAudit ||
			row.process.businessType === FlowAuditTypes.ReportTaskTranspondAudit
		) {
			router.push({
				path: '/taskPending/report-task-detail',
				query: {
					taskId: row.id,
					reportTaskId:
						row.process.businessExtend.Id || row.process.businessExtend.ReportTaskId,
					areaOrganizationUnitId:
						row.process.businessExtend.Id ||
						row.process.businessExtend.AreaOrganizationUnitId,
					id: row.process.businessExtend.Id,
					type: 'audit',
					currentIndex: 4,
					businessType: row.process.businessType,
				},
			})
		} else if (row.process.businessType === FlowAuditTypes.LedgerDataExport) {
			router.push({
				path: '/taskPending/export-audit',
				query: {id: row.process.businessId, rid: row.processId, tid: row.id},
			})
		} else {
			router.push({
				path: '/taskPending/task-review-details',
				query: {
					id: row.id,
					taskId: row.process.id,
					keyword: row.process.keyword2,
					businessId: row.process.businessId,
					batchType: row.process.businessExtend.Type,
					Remark: row.process.businessExtend.Remark,
				},
			})
		}
	}
}

const setShowAddRule = () => {
	if (addRulesRef && addRulesRef.value) {
		addRulesRef.value.open()
	}
}


const isCityMechanism=ref(false)
const isCountyMechanism=ref(false)
</script>
<template>
	<div class="temporary-report">
		<Block
			title="临时报表准入"
			:enable-fixed-height="true"
			@heightChanged="onBlockHeightChanged"
		>
			<template #topRight>
				<el-button size="small" type="primary" @click="showTodoRemind = true">
					办理记录
				</el-button>
				<el-button size="small" type="primary" @click="onBatchAudit(1)">批量审核</el-button>
				<el-button size="small" type="primary" @click="onBatchAudit(0)">全部审核</el-button>
				<el-button size="small" type="primary" @click="$router.push('/processEngine')">
					审核流程配置
				</el-button>
			</template>
			<template #expand>
				<div class="search">
					<Form
						:props="formProps"
						v-model="form"
						:column-count="2"
						:label-width="74"
						:enable-reset="false"
						confirm-text="查询"
						button-vertical="flowing"
						@submit="onSearch"
					></Form>
				</div>
			</template>
			<div class="btn-box" style="margin-bottom: 12px">
				<el-button @click="setShowAddRule" type="primary">整体准入规则设置</el-button>
				<el-button @click="isCityMechanism=true" type="primary">市级部门准入机制</el-button>
				<el-button @click="isCountyMechanism=true" type="primary">区级部门准入机制</el-button>
			</div>
			<TableV2
				ref="tableRef"
				url="/api/workflow/workflowTask/my-unCompleted"
				:columns="columns"
				:req-params="reqParams"
				:height="tableHeight"
				:enable-toolbar="false"
				:enable-selection="true"
				:enable-own-button="false"
				:buttons="[{label: '审核', code: 'toexamine', type: 'primary'}]"
				@click-button="onTableButtonClick"
				@before-complete="beforeTableComplete"
				@completed="
					() => {
						pagination.total = tableRef.getTotal()
					}
				"
			>
				<template #title="{row}">
					{{ row.process?.title }}
				</template>
				<template #state="{row}">
					<span
						:style="{color: statusArr.filter((x:any) => x.state === row.state)[0].color}"
					>
						{{ statusArr.filter((x: any) => x.state === row.state)[0].name }}
					</span>
				</template>
				<template #_category="{row}">
					{{ FlowAuditTypeData[row.process?.businessType].name }}
				</template>
			</TableV2>
			<Pagination
				:total="pagination.total"
				:current-page="pagination.page"
				:page-size="pagination.size"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			></Pagination>
		</Block>

		<Dialog
			v-model="showAuditDialog"
			:title="isBatchAuditing ? '批量审核' : '全部审核'"
			@click-confirm="onAuditConfirm"
		>
			<Form
				v-model="auditForm"
				ref="auditFormRef"
				:props="[
					{
						label: '审核结果',
						prop: 'code',
						type: 'radio',
						options: [
							{label: '通过', value: 'agree'},
							{label: '驳回', value: 'disagree'},
						],
					},
					{
						label: '审核意见',
						prop: 'des',
						type: 'textarea',
					},
				]"
				:rules="{
					code: [{required: true, message: '请选择审核结果', trigger: 'blur'}],
					des: [{required: true, message: '请输入审核意见', trigger: 'blur'}],
				}"
				:enable-button="false"
			></Form>
		</Dialog>

		<Dialog
			title="办理记录"
			v-model="showTodoRemind"
			:destroy-on-close="true"
			:enable-confirm="false"
			width="1000"
		>
			<TableV2
				ref="tableRefRecord"
				url="/api/workflow/workflowTask/my-completed"
				:auto-height="true"
				:columns="recordColumns"
				:req-params="reqParamsRecord"
				:enable-toolbar="false"
				:enable-own-button="false"
				:buttons="[{label: '查看', code: 'view', type: 'primary'}]"
				@click-button="onTableButtonClick"
				@before-complete="beforeRecordComplete"
				@completed="
					() => {
						paginationRecord.total = tableRefRecord.getTotal()
					}
				"
			>
				<template #title="{row}">{{ row.process?.title }}</template>
				<template #outcome="{row}">
					<span
						:style="{
							color: statusArr2.filter(
								(x) => x.state === (!row.isAgree ? 2 : row.outcome)
							)[0].color,
						}"
					>
						{{
							statusArr2.filter(
								(x) => x.state === (!row.isAgree ? 2 : row.outcome)
							)[0].name
						}}
					</span>
				</template>
			</TableV2>
			<Pagination
				:total="paginationRecord.total"
				:current-page="paginationRecord.page"
				:page-size="paginationRecord.size"
				@current-change="onPaginationRecordChange($event, 'page')"
				@size-change="onPaginationRecordChange($event, 'size')"
			></Pagination>
		</Dialog>
		<AddRules ref="addRulesRef" />
		<CityMechanism v-model="isCityMechanism" title="市级部门准入规则管理" width="1000px" />
		<CityMechanism v-model="isCountyMechanism" title="区级部门准入规则管理" width="1000px" />
	</div>
</template>
<route>
    {
		meta: {
			title: '临时报表准入',
		},
	}
</route>
<style scoped lang="scss"></style>
