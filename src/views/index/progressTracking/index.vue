<script lang="ts" setup name="tasklink">
import {onMounted, ref, reactive, computed} from 'vue'
import {useRouter} from 'vue-router'
import {allRegionNames, myRegionNames} from '@/api/LedgerApi'
import {
	// 删除任务绑定台账和台账配置
	deleteLedgerApi,
	// 确认关联
	makeSurePlanProgressConfigApi,
} from '@/api/taskLinkApi'
import {ElMessage, ElMessageBox, ElNotification} from 'element-plus'
import {ArrowDown} from '@element-plus/icons-vue'

const router = useRouter()

// 子任务弹窗列表请求参数
const reqParamsSon = reactive({
	TaskName: '', // 任务名称
	Status: '2', // 电信任务状态：1-待关联，2-已关联
	parentId: '',

	skipCount: 0,
	maxResultCount: 10,
})
// 获取村社列表
const regionNamesOptions = ref<any>([])
function getRegionNames() {
	allRegionNames().then((res) => {
		if (res.data.length > 0) {
			regionNamesOptions.value = res.data
			// regionNamesOptions.value.unshift({
			// 	id: 1,
			// 	name: '本镇街',
			// })
		}
	})
}
// 搜索
const formItems = ref([
	{
		prop: 'RegionId',
		type: 'text',
		placeholder: '请输入任务名称',
		formShow:
			JSON.parse(localStorage.getItem('currentUserInfo') as string).baseRoles.includes(
				'管理员'
			) &&
			JSON.parse(localStorage.getItem('currentDepartmentInfo') as any).community == null &&
			JSON.parse(localStorage.getItem('currentDepartmentInfo') as any).street == null &&
			JSON.parse(localStorage.getItem('currentDepartmentInfo') as any).district == null,
	},
	{
		prop: 'TelecomCode',
		type: 'text',
		placeholder: '请输入任务名称',
		formShow:
			JSON.parse(localStorage.getItem('currentUserInfo') as string).staffRole.includes(
				'区县台账运维员'
			) ||
			(JSON.parse(localStorage.getItem('currentUserInfo') as string).baseRoles.includes(
				'管理员'
			) &&
				JSON.parse(localStorage.getItem('currentDepartmentInfo') as any).community ==
					null &&
				JSON.parse(localStorage.getItem('currentDepartmentInfo') as any).street == null &&
				JSON.parse(localStorage.getItem('currentDepartmentInfo') as any).district !== null),
	},
	{
		prop: 'TaskName',
		type: 'text',
		placeholder: '请输入任务名称',
	},
	{
		prop: 'Runway',
		type: 'select',
		placeholder: '请选择任务所属板块',
		options: [
			// 1-待关联，2-已关联
			{label: '党的建设', value: '党的建设'},
			{label: '经济发展', value: '经济发展'},
			{label: '民生服务', value: '民生服务'},
			{label: '平安法治', value: '平安法治'},
		],
	},
	{
		prop: 'date',
		type: 'daterange',
	},

	// {
	// 	prop: 'Status',
	// 	type: 'select',
	// 	placeholder: '请选择任务状态',
	// 	options: [
	// 		// 1-待关联，2-已关联
	// 		{label: '待关联', value: 1},
	// 		{label: '已关联', value: 2},
	// 	],
	// },
])
const searchForm = ref<any>({})

enum RunwayType {
	'所有任务', // 不传
	'党的建设',
	'经济发展',
	'民生服务',
	'平安法治',
}

let numList = ref<any>({
	所有任务: 0, // 不传
	党的建设: 0,
	经济发展: 0,
	民生服务: 0,
	平安法治: 0,
})

const activeName = ref(RunwayType[0])

const reqParams = reactive({
	Runway: '', // 党的建设、经济发展、民生服务、平安法治
	TaskName: '', // 任务名称
	StartTime: '', // 开始时间
	EndTime: '',
	Status: 2, // 电信任务状态：1-待关联，2-已关联
	telecomCode: '',
	regionId: '',
	skipCount: 0,
	maxResultCount: 10,
})

// 搜索
const onSearch = (type?: string) => {
	if (type === 'search') {
		reqParams.TaskName = searchForm.value?.TaskName
		reqParams.StartTime = searchForm.value?.date?.[0]
		reqParams.EndTime = searchForm.value?.date?.[1]
		reqParams.Status = 2
		reqParams.Runway = searchForm.value?.Runway
		reqParams.telecomCode = searchForm.value?.TelecomCode
		reqParams.regionId = searchForm.value?.RegionId
	}

	if (type === 'clear') {
		reqParams.TaskName = ''
		reqParams.StartTime = ''
		reqParams.EndTime = ''
		reqParams.Status = 2
		reqParams.Runway = ''
		reqParams.telecomCode = ''
		reqParams.regionId = ''
		searchForm.value = {}
	}
}

// 表格
const tableRef = ref()
const tableHeight = ref(0)
const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75 // Block 内容高度 - 分页高度
}

// 任务状态 1-待关联，2-已关联
const FlowTaskStatus = [
	{label: '待关联', value: 1, color: '#ff8515'},
	{label: '已关联', value: 2, color: '#78bf4a'},
]

const tableColumns = ref([
	{prop: 'taskName', label: '任务名称', width: 200},
	{prop: 'belongBlock', label: '所属板块'}, //
	{prop: 'ledgerRunway', label: '核心业务', width: 200}, //
	{prop: 'pushType', label: '任务来源', width: 200},
	{prop: 'streetTown', label: '所属街镇',width: 300 },
	{prop: 'leaders', label: '牵头领导'},
	{prop: 'date', label: '起止日期', width: 300},
	{prop: 'chargePersons', label: '责任人', width: 140}, //chargePersons[] .chargePersonUsername
	// string (1任意责任人完成即可 0全部责任人均需完成)
	{prop: 'taskCompleteCon', label: '完成条件', width: 250},
	{prop: 'targetName', label: '关联目标', width: 300},
	{prop: 'ledgerName', label: '关联业务表', width: 200},
	{prop: 'childCount', label: '子任务'},

	{prop: 'status', label: '关联状态', fixed: 'right'}, // 1-待关联，2-已关联
	{prop: 'progress', label: '进度', fixed: 'right'},
	{prop: 'remainingWork', label: '剩余工作量', width: 120, fixed: 'right'},
])

const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})

// 顶部tab切换
const onClickTabs = (val: any) => {
	console.log('onClickTabs----', val)
	activeName.value = val
	val === '所有任务' ? (reqParams.Runway = '') : (reqParams.Runway = val)

	if (val !== '所有任务' && tableColumns.value[1].label === '所属板块') {
		tableColumns.value.splice(1, 1)
	} else if (val === '所有任务') {
		tableColumns.value.splice(1, 0, {prop: 'belongBlock', label: '所属板块'})
	}
}

const onTableCompleted = () => {
	pagination.total = tableRef.value.getTotal()

	console.log('onTableCompleted', pagination.total)
}

function handleDetail(row: any) {
	console.log('handleDetail', row)
	if (row.status === 1) {
		onTableButtonClick({btn: {code: 'link'}, row})
	} else if (row.status === 2) {
		onTableButtonClick({btn: {code: 'detail'}, row})
	}
}
// 点击表格的操作按钮
const onTableButtonClick = ({btn, row, index}: any) => {
	console.log('点击表格的操作按钮---', btn.code, row, index)
	if (btn.code === 'showSon') {
		reqParamsSon.parentId = row.id

		return (dialogSon.value = true)
	}

	let path
	if (btn.code === 'detail') {
		// if (row.ledgerId) row.ledgerId ? (path = '/tasklink/detail') : '/tasklink/link' // 测试
		path = '/progressTracking/detail'
	} else if (btn.code === 'link') {
		path = '/progressTracking/link'
	}
	router.push({
		path,
		query: {
			id: row.id,
			type: btn.code,
		},
	})
}
// 计算剩余工作量
const calculateRemainingWork = (row: any): string => {
	// 根据进度计算剩余条数，这里使用模拟数据
	const progress = row.progress || 0
	// 假设总工作量为100-500条之间的随机数
	const totalWork = Math.floor(Math.random() * 400) + 100
	// 根据进度计算剩余工作量
	const remainingWork = Math.floor(totalWork * (100 - progress) / 100)
	return remainingWork.toString()
}

onMounted(() => {
	// getNum()
	getRegionNames()
	// 初始化动画配置
	initAnimationConfig()
})

const getNum = async () => {
	// const res0 = await getListApi({Runway: ''})
	// numList.value['所有任务'] = res0.data.totalCount
	// const res1 = await getListApi({Runway: '党的建设'})
	// numList.value['党的建设'] = res1.data.totalCount
	// const res2 = await getListApi({Runway: '经济发展'})
	// numList.value['经济发展'] = res2.data.totalCount
	// const res3 = await getListApi({Runway: '民生服务'})
	// numList.value['民生服务'] = res3.data.totalCount
	// const res4 = await getListApi({Runway: '平安法治'})
	// numList.value['平安法治'] = res4.data.totalCount
}

const onPaginationChange = (val: any, type: any) => {
	if (type == 'page') {
		pagination.page = val
		reqParams.skipCount = (val - 1) * pagination.size
	} else {
		pagination.size = val
		reqParams.maxResultCount = pagination.size
	}
}

// 子任务列表弹窗
let dialogSon: any = ref(false)
const tableHeightSon = ref(0)

const heightChanged = (height: any) => {
	tableHeightSon.value = height - 75 // Block 内容高度 - 分页高度
}
let currentUserInfoRole = JSON.parse(localStorage.getItem('currentUserInfo') as any)
let currentDepartmentInfo = JSON.parse(localStorage.getItem('currentDepartmentInfo') as any)
// 搜索表单star
const searchFormSon = ref<any>({})

const formItemsSon = ref([
	{
		prop: 'TaskName',
		type: 'text',
		placeholder: '请输入任务名称',
	},
	{
		prop: 'Status',
		type: 'select',
		placeholder: '请选择任务状态',
		options: [
			// 1-待关联，2-已关联
			{label: '待关联', value: 1},
			{label: '已关联', value: 2},
		],
	},
])

const onSearchSon = (type?: string) => {
	if (type === 'search') {
		reqParamsSon.TaskName = searchFormSon.value?.TaskName
		reqParamsSon.Status = '2'
	}

	if (type === 'clear') {
		reqParamsSon.TaskName = ''
		reqParamsSon.Status = '2'
	}
}

// 搜索表单end

// 子任务表格
const tableRefSon = ref()

const tableColumnsSon = ref([
	{prop: 'taskName', label: '任务名称', width: 200},
	{prop: 'endTime', label: '完成截止时间', width: 200}, //
	{prop: 'ledgerName', label: '关联业务表', width: 200},
	{prop: 'status', label: '关联状态'}, // 1-待关联，2-已关联
	{prop: 'progress', label: '进度'},
])

const tableButtonsSon = ref([
	{
		code: 'detail',
		label: '查看',
		show: `row.status ===2`,
	},
	{
		code: 'link',
		label: '关联业务表',
		type: 'primary',
		show: `row.status ===1`,
	},
])

// const pagination = reactive({
// 	page: 1,
// 	size: 10,
// 	total: 0,
// }) f5271b5aa81b41c78ef241d58d0230007

const onTableCompletedSon = () => {
	paginationSon.total = tableRefSon.value.getTotal()
}
// 点击表格的操作按钮
const onTableButtonClickSon = ({btn, row, index}: any) => {
	let path = ''
	if (btn.code === 'detail') {
		path = '/progressTracking/detailSon'
	} else if (btn.code === 'link') {
		path = '/progressTracking/linkSon'
	}
	dialogSon.value = false
	router.push({
		path,
		query: {
			id: row.id,
			type: btn.code,
		},
	})
}
const paginationSon = reactive({
	page: 1,
	size: 10,
	total: 0,
})

const onPaginationChangeSon = (val: any, type: any) => {
	if (type == 'page') {
		paginationSon.page = val
		reqParamsSon.skipCount = (val - 1) * paginationSon.size
	} else {
		paginationSon.size = val
		reqParamsSon.maxResultCount = paginationSon.size
	}
}

// 更改配置
const openConfig = (id: string) => {
	ElMessageBox.confirm('更改配置后已计算的任务进度将会重新计算，是否更改？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			router.push({
				path: '/progressTracking/link',
				query: {
					id,
				},
			})
		})
		.catch(() => {})
}
const openConfigSon = (id: string) => {
	ElMessageBox.confirm('更改配置后已计算的任务进度将会重新计算，是否更改？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			dialogSon.value = false
			router.push({
				path: '/progressTracking/linkSon',
				query: {
					id,
				},
			})
		})
		.catch(() => {})
}

// 取消关联
const openCancel = (id: string) => {
	ElMessageBox.confirm(
		'取消关联后已计算的任务进度将会清空，且状态变为待关联，是否取消？',
		'提示',
		{
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		}
	)
		.then(async () => {
			// 绑定删除任务绑定台账和台账配置台账 taskId,
			await deleteLedgerApi(id)
			await makeSurePlanProgressConfigApi(id, {
				status: 1, // 1 取消关联 2 关联 -- id是页面详情的id
			})
			// 更新数据
			tableRef.value.reload()
			ElMessage({
				type: 'success',
				message: '操作成功',
			})
		})
		.catch(() => {
			ElMessage({
				type: 'info',
				message: '操作失败',
			})
		})
}
const openCancelSon = (id: string) => {
	ElMessageBox.confirm(
		'取消关联后已计算的任务进度将会清空，且状态变为待关联，是否取消？',
		'提示',
		{
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		}
	)
		.then(async () => {
			// 绑定删除任务绑定台账和台账配置台账 taskId,
			await deleteLedgerApi(id)
			await makeSurePlanProgressConfigApi(id, {
				status: 1, // 1 取消关联 2 关联 -- id是页面详情的id
			})
			// 更新数据
			tableRefSon.value.reload()
			ElMessage({
				type: 'success',
				message: '操作成功',
			})
		})
		.catch(() => {
			ElMessage({
				type: 'info',
				message: '操作失败',
			})
		})
}
const sercrdAll = ref<any>([])
const isAdmin = computed(() => {
	return (
		currentUserInfoRole.baseRoles.includes('管理员') ||
		currentUserInfoRole.staffRole.includes('区县台账运维员')
	)
})
const onFilterFieldSelectChange = (val: any) => {
	if (val) {
		myRegionNames({}, {guid: val}).then((res: any) => {
			sercrdAll.value = res.data
		})
	}
	searchForm.value.TelecomCode = ''
}
const remindFunc = () => {
	urging.value = true
}
const urging = ref(false)
const urgingList = ref([])

// 进度动画设置相关状态
const showProgressAnimationDialog = ref(false)
const showColorPicker = ref(false)
const currentColorType = ref('') // 'progress' | 'loading'

// 进度条动画配置
const progressAnimationConfig = ref({
	type: 'standard', // 'standard' | 'circle' | 'text' | 'none'
	color: '#409EFF'
})

// 加载动画配置
const loadingAnimationConfig = ref({
	type: 'spin', // 'spin' | 'pulse' | 'bar' | 'fade' | 'skeleton'
	color: '#409EFF'
})

// 进度条动画类型选项
const progressAnimationTypes = [
	{ type: 'standard', name: '进度条', description: '标准进度条显示', demo: 'progress-bar' },
	{ type: 'circle', name: '环形', description: '环形进度显示', demo: 'circle' },
	{ type: 'text', name: '文字', description: '纯文字百分比', demo: 'text' },
	{ type: 'none', name: '无动画', description: '静态显示', demo: 'none' }
]

// 加载动画类型选项
const loadingAnimationTypes = [
	{ type: 'spin', name: '旋转圆圈', description: '旋转加载动画', demo: 'spin' },
	{ type: 'pulse', name: '脉冲点', description: '脉冲加载动画', demo: 'pulse' },
	{ type: 'bar', name: '进度条', description: '进度条加载', demo: 'bar' },
	{ type: 'fade', name: '渐入效果', description: '渐入淡出效果', demo: 'fade' },
	{ type: 'skeleton', name: '骨架屏', description: '骨架屏加载', demo: 'skeleton' }
]

// 预设颜色选项与名称
const presetColorsWithNames = ref([
	{ color: '#409EFF', name: '政务蓝' },
	{ color: '#67C23A', name: '政务绿' },
	{ color: '#E6A23C', name: '政务金' },
	{ color: '#F56C6C', name: '分散色' }
])

// 向下兼容的预设颜色数组
const presetColors = computed(() => presetColorsWithNames.value.map(item => item.color))

// 预设颜色
const presetColorsList = [
	{ name: '政务蓝', color: '#409EFF' },
	{ name: '政务绿', color: '#67C23A' },
	{ name: '政务金', color: '#E6A23C' },
	{ name: '分段灰色', color: '#909399' },
	{ name: '自定义', color: 'custom' }
]
const currentChild = ref([
	{
		label: 1,
		name: '发送渝快政工作通知',
	},
	{
		label: 2,
		name: '发送渝快政Ding消息',
	},
	{
		label: 3,
		name: '发送系统内部通知',
	},
])
const onClickChild = (child: any) => {
	console.log(child)
}
const currentIndex = ref(1)
const confirmUrging = async () => {
	if (!urgingList.value.length) {
		ElMessage.warning('请选择催办通知方式')
		return
	}
	switch (currentIndex.value) {
		case 1:
			ElNotification.success('已通知相关部门')
			urging.value = false
			urgingList.value = []
			break
		case 2:
			ElNotification.success('已通知相关人员')
			urging.value = false
			urgingList.value = []
			break
	}
}

// 进度动画设置相关函数
// 表格Loading状态
const tableLoading = ref(false)

// 实际应用的配置（用于主列表渲染）
const appliedProgressConfig = ref({
	type: 'standard',
	color: '#409EFF'
})

const appliedLoadingConfig = ref({
	type: 'spin',
	color: '#409EFF'
})

// 弹窗中的临时配置（用于预览）
const tempProgressConfig = ref({
	type: 'standard',
	color: '#409EFF'
})

const tempLoadingConfig = ref({
	type: 'spin',
	color: '#409EFF'
})

// 进度动画类型选项
const progressAnimationTypeOptions = ref([
	{ type: 'standard', name: '标准进度条', description: '经典水平进度条样式' },
	{ type: 'circle', name: '环形进度', description: '圆环形进度显示' },
	{ type: 'text', name: '文本进度', description: '纯文本百分比显示' },
	{ type: 'none', name: '无动画', description: '静态数值显示' }
])

// 加载动画类型选项
const loadingAnimationTypeOptions = ref([
	{ type: 'spin', name: '旋转加载', description: '经典旋转菊花图' },
	{ type: 'pulse', name: '脉冲加载', description: '渐变脉冲效果' },
	{ type: 'bar', name: '条形加载', description: '水平进度条加载' },
	{ type: 'fade', name: '渐入加载', description: '淡入淡出效果' },
	{ type: 'skeleton', name: '骨架屏', description: '骨架屏加载效果' }
])

// 刷新表格数据（带Loading效果）
async function refreshTableData() {
	tableLoading.value = true
	console.log('刷新表格数据，使用加载动画:', loadingAnimationConfig.value.type)
	
	// 模拟数据加载延迟（演示Loading效果）
	await new Promise(resolve => setTimeout(resolve, 1500))
	
	// 这里实现表格数据刷新逻辑
	// 可以调用相关API重新获取数据
	
	tableLoading.value = false
	console.log('表格数据刷新完成')
}

// 应用所有动画配置
async function applyAnimationConfig() {
	// 将临时配置应用到实际配置
	appliedProgressConfig.value = { ...tempProgressConfig.value }
	appliedLoadingConfig.value = { ...tempLoadingConfig.value }
	
	// 保存配置到IndexedDB
	await saveConfigToIndexedDB()
	
	// 刷新主列表以应用新的动画配置
	await refreshTableData()
	
	console.log('应用进度动画配置:', appliedProgressConfig.value)
	console.log('应用加载动画配置:', appliedLoadingConfig.value)
	
	ElMessage.success('动画配置已应用')
}

// 应用进度条动画配置（兼容旧函数名）
async function applyProgressConfig() {
	await applyAnimationConfig()
}

// 应用加载动画配置（兼容旧函数名）
async function applyLoadingConfig() {
	await applyAnimationConfig()
}

// 初始化配置数据
async function initAnimationConfig() {
	const loaded = await loadConfigFromIndexedDB()
	if (loaded) {
		console.log('已从数据库加载进度动画配置')
	} else {
		console.log('使用默认进度动画配置')
	}
}

// 选择进度条动画类型（临时配置）
function selectTempProgressType(type: string) {
	tempProgressConfig.value.type = type
}

// 选择加载动画类型（临时配置）
function selectTempLoadingType(type: string) {
	tempLoadingConfig.value.type = type
}

// 兼容旧函数名
function selectProgressAnimationType(type: string) {
	selectTempProgressType(type)
}

function selectLoadingAnimationType(type: string) {
	selectTempLoadingType(type)
}

// 选择预设颜色
function selectPresetColor(color: string, type: 'progress' | 'loading') {
	if (color === 'custom') {
		// 打开自定义颜色选择器
		openColorPicker(type)
	} else {
		selectColor(type, color)
	}
}

// 选择临时颜色
function selectTempColor(type: string, color: string) {
	if (type === 'progress') {
		tempProgressConfig.value.color = color
	} else if (type === 'loading') {
		tempLoadingConfig.value.color = color
	}
}

// 选择颜色（兼容旧函数名）
function selectColor(type: string, color: string) {
	selectTempColor(type, color)
}

// 打开自定义颜色选择器
function openColorPicker(type: string) {
	showColorPicker.value = true
	currentColorType.value = type
}

// 打开自定义颜色选择器（兼容旧函数名）
function openCustomColorPicker(type: string) {
	openColorPicker(type)
}

// IndexedDB数据库操作
const DB_NAME = 'ProgressAnimationDB'
const DB_VERSION = 1
const STORE_NAME = 'animationConfig'

// 打开或创建IndexedDB数据库
function openIndexedDB(): Promise<IDBDatabase> {
	return new Promise((resolve, reject) => {
		const request = indexedDB.open(DB_NAME, DB_VERSION)
		
		request.onerror = () => reject(request.error)
		request.onsuccess = () => resolve(request.result)
		
		request.onupgradeneeded = (event) => {
			const db = (event.target as IDBOpenDBRequest).result
			if (!db.objectStoreNames.contains(STORE_NAME)) {
				db.createObjectStore(STORE_NAME, { keyPath: 'id' })
			}
		}
	})
}

// 保存配置到IndexedDB
async function saveConfigToIndexedDB() {
	try {
		const db = await openIndexedDB()
		const transaction = db.transaction([STORE_NAME], 'readwrite')
		const store = transaction.objectStore(STORE_NAME)
		
		const config = {
			id: 'progressAnimationConfig',
			progressAnimation: {
				type: appliedProgressConfig.value.type,
				color: appliedProgressConfig.value.color
			},
			loadingAnimation: {
				type: appliedLoadingConfig.value.type,
				color: appliedLoadingConfig.value.color
			},
			updatedAt: new Date().toISOString()
		}
		
		await new Promise<void>((resolve, reject) => {
			const request = store.put(config)
			request.onsuccess = () => resolve()
			request.onerror = () => reject(request.error)
		})
		
		db.close()
		console.log('进度动画配置已保存到IndexedDB')
	} catch (error) {
		console.error('保存配置到IndexedDB失败:', error)
		// 降级到localStorage
		localStorage.setItem('progressAnimationConfig', JSON.stringify({
			progressAnimation: progressAnimationConfig.value,
			loadingAnimation: loadingAnimationConfig.value
		}))
	}
}

// 从IndexedDB加载配置
async function loadConfigFromIndexedDB() {
	try {
		const db = await openIndexedDB()
		const transaction = db.transaction([STORE_NAME], 'readonly')
		const store = transaction.objectStore(STORE_NAME)
		
		const config = await new Promise<any>((resolve, reject) => {
			const request = store.get('progressAnimationConfig')
			request.onsuccess = () => resolve(request.result)
			request.onerror = () => reject(request.error)
		})
		
		db.close()
		
		if (config) {
			// 初始化应用配置（用于主列表渲染）
			appliedProgressConfig.value = {
				type: config.progressAnimation?.type || 'standard',
				color: config.progressAnimation?.color || '#409EFF'
			}
			appliedLoadingConfig.value = {
				type: config.loadingAnimation?.type || 'spin',
				color: config.loadingAnimation?.color || '#409EFF'
			}
			
			// 初始化临时配置（用于弹窗预览）
			tempProgressConfig.value = { ...appliedProgressConfig.value }
			tempLoadingConfig.value = { ...appliedLoadingConfig.value }
			
			console.log('已从IndexedDB加载进度动画配置')
			return true
		}
	} catch (error) {
		console.error('从IndexedDB加载配置失败:', error)
		// 降级到localStorage
		try {
			const localConfig = localStorage.getItem('progressAnimationConfig')
			if (localConfig) {
				const config = JSON.parse(localConfig)
				progressAnimationConfig.value = config.progressAnimation || progressAnimationConfig.value
				loadingAnimationConfig.value = config.loadingAnimation || loadingAnimationConfig.value
				console.log('已从localStorage加载进度动画配置')
				return true
			}
		} catch (localError) {
			console.error('从localStorage加载配置失败:', localError)
		}
	}
	return false
}

// 自定义颜色确认
function confirmCustomColor(color: string | null) {
	if (!color) return
	if (currentColorType.value === 'progress') {
		progressAnimationConfig.value.color = color
	} else if (currentColorType.value === 'loading') {
		loadingAnimationConfig.value.color = color
	}
	showColorPicker.value = false
	currentColorType.value = ''
}



// 关闭进度动画设置弹窗
function closeProgressAnimationDialog() {
	showProgressAnimationDialog.value = false
	showColorPicker.value = false
	currentColorType.value = ''
}

// 处理更多操作下拉菜单
function handleDropdownCommand(command: string) {
	switch (command) {
		case 'multiAnalysis':
			// 跳转到多维度统计分析页面
			router.push('/progressTracking/multiAnalysis')
			break
		case 'progressAnimation':
			// 打开进度动画设置页面
			showProgressAnimationDialog.value = true
			break
	}
}

</script>
<template>
	<div class="tasklink">
		<Block
			title="进度追踪"
			:enable-fixed-height="true"
			:enable-expand-content="true"
			:enable-back-button="false"
			@heightChanged="onBlockHeightChanged"
		>
			<template #topRight>
				<div style="display: flex; align-items: center; gap: 12px;">
					<el-dropdown @command="handleDropdownCommand">
						<el-button type="primary" size="small">
							更多操作
							<el-icon class="el-icon--right"><arrow-down /></el-icon>
						</el-button>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item command="multiAnalysis">多维度统计分析</el-dropdown-item>
								<el-dropdown-item command="progressAnimation">进度动画设置</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</div>
			</template>
			<!-- <template #title>
				<el-tabs v-model="activeName" class="tabs-ui" @tab-change="onClickTabs">
					<el-tab-pane :label="`所有任务(${numList['所有任务']})`" name="所有任务"></el-tab-pane>
					<el-tab-pane :label="`党的建设(${numList['党的建设']})`" name="党的建设"></el-tab-pane>
					<el-tab-pane :label="`经济发展(${numList['经济发展']})`" name="经济发展"></el-tab-pane>
					<el-tab-pane :label="`民生服务(${numList['民生服务']})`" name="民生服务"></el-tab-pane>
					<el-tab-pane :label="`平安法治(${numList['平安法治']})`" name="平安法治"></el-tab-pane>
				</el-tabs>
			</template> -->

			<template #expand>
				<div class="search">
					<Form
						v-model="searchForm"
						:props="formItems"
						:enable-reset="false"
						label-width="0"
						column-count="5"
						confirm-text="查询"
						@submit="onSearch('search')"
						@clear="onSearch('clear')"
					>
						<template
							#form-RegionId
							v-if="
								currentUserInfoRole.baseRoles.includes('管理员') &&
								currentDepartmentInfo.community == null &&
								currentDepartmentInfo.street == null &&
								currentDepartmentInfo.district == null
							"
						>
							<div style="display: flex; width: 100%">
								<el-select
									clearable
									style="width: 50%; margin-right: 10px"
									v-model="searchForm.RegionId"
									@keyup.enter="onSearch('search')"
									@change="onFilterFieldSelectChange"
								>
									<el-option
										v-for="item in regionNamesOptions"
										:key="item.id"
										:label="item.name"
										:value="item.id"
									/>
								</el-select>
								<el-select
									v-model="searchForm.TelecomCode"
									style="width: 50%"
									@keyup.enter="onSearch('search')"
									clearable
								>
									<el-option
										v-for="condition of sercrdAll"
										:value="condition.telecomCode"
										:label="condition.name"
									/>
								</el-select>
							</div>
						</template>
						<template
							#form-TelecomCode
							v-if="
								currentUserInfoRole.staffRole.includes('区县台账运维员') ||
								(currentUserInfoRole.baseRoles.includes('管理员') &&
									currentDepartmentInfo.community == null &&
									currentDepartmentInfo.street == null &&
									currentDepartmentInfo.district !== null)
							"
						>
							<div style="width: 100%">
								<el-select
									clearable
									@keyup.enter="onSearch('search')"
									v-model="searchForm.TelecomCode"
								>
									<el-option
										v-for="item in regionNamesOptions"
										:key="item.id"
										:label="item.name"
										:value="item.telecomCode"
									/>
								</el-select>
							</div>
						</template>
					</Form>
				</div>
			</template>

			<TableV2
				ref="tableRef"
				url="/api/ledger/telecom-task"
				:headers="{Urlkey: 'base'}"
				:req-params="reqParams"
				:columns="tableColumns"
				:enable-edit="false"
				:enable-delete="false"
				:enable-selection="false"
				:enable-latest-data="false"
				:height="tableHeight"
				:enable-toolbar="false"
				:form-label-width="100"
				:loading="tableLoading"
				@completed="onTableCompleted"
				@click-button="onTableButtonClick"
			>
				<template #taskName="{row}">
					<span class="link-click" @click="isAdmin ? '' : handleDetail(row)">{{
						row.taskName
					}}</span>
				</template>
				<template #pushType="{row}">
					<span>{{ row.pushType == 0 ? '基层智治' : '三级平台' }}</span>
				</template>
				<template #streetTown="{row}">
					<span>{{ row.county?  row.county :''}}-{{ row.street? row.street:'' }}</span>
				</template>
				<!-- 所属板块 -->
				<template #belongBlock="{row}">
					<div v-if="row.businessName && row.businessName.indexOf('/') > 0">
						<span>{{ row.businessName.split('/')[0] }}</span>
					</div>
					<div v-else>-</div>
				</template>

				<!-- 核心业务 -->
				<template #ledgerRunway="{row}">
					<div v-if="row.businessName && row.businessName.indexOf('/') > 0">
						<span>{{ row.businessName.split('/')[1] }}</span>
						<span v-if="row.businessName.split('/')[2]"
							>/{{ row.businessName.split('/')[2] }}</span
						>
					</div>
					<div v-else>-</div>
				</template>

				<!-- 起止时间 -->
				<template #date="{row}">
					<div v-if="row.startTime">
						<span>{{ row.startTime }}~{{ row.endTime }}</span>
					</div>
				</template>

				<!-- 责任人 -->
				<template #chargePersons="{row}">
					<div v-if="row.chargePersons.length">
						<!-- chargePersonUsername -->
						<!-- <span v-for="(item, idx) in row.chargePersons">{{ item.chargePersonUsername }}、</span> -->
						{{ row.chargePersons.map((item: { chargePersonName: string }) => item.chargePersonName).toString() }}
					</div>
					<div v-else></div>
				</template>

				<!-- 完成条件  任务完成条件(1任意责任人完成即可 0全部责任人均需完成) -->
				<template #taskCompleteCon="{row}">
					<span v-if="row.taskCompleteCon === '1'">任意责任人完成即可</span>
					<span v-else-if="row.taskCompleteCon === '0'">全部责任人均需完成</span>
				</template>

				<!-- {prop: 'childCount', label: '子任务'}, -->
				<template #childCount="{row}">
					<span
						class="link-click"
						@click="onTableButtonClick({btn: {code: 'showSon'}, row})"
						>{{ row.childCount }}</span
					>
				</template>

				<!-- 关联状态 -->
				<template #status="{row}">
					<div flex="~" items-center>
						<span
							mr-5px
							inline-block
							w-10px
							h-10px
							rounded-full
							:style="{
								backgroundColor: FlowTaskStatus.filter(
									(v) => v.value === row.status
								)[0].color,
							}"
						></span>
						<span
							:style="{
								color: FlowTaskStatus.filter((v) => v.value === row.status)[0]
									.color,
							}"
						>
							{{ FlowTaskStatus.filter((v) => v.value === row.status)[0].label }}
						</span>
					</div>
				</template>

				<template #progress="{row}">
					<!-- 根据应用配置动态渲染进度显示 -->
					<div v-if="appliedProgressConfig.type === 'standard'" class="progress-standard">
						<el-progress
							:percentage="parseFloat(row.progress) || 0"
							:width="120"
							:stroke-width="8"
							:show-text="true"
							:color="appliedProgressConfig.color"
						/>
					</div>
					
					<div v-else-if="appliedProgressConfig.type === 'circle'" class="progress-circle">
						<el-progress
							:percentage="parseFloat(row.progress) || 0"
							type="circle"
							:width="50"
							:stroke-width="6"
							:color="appliedProgressConfig.color"
						/>
					</div>
					
					<div v-else-if="appliedProgressConfig.type === 'text'" class="progress-text">
						<span 
							class="progress-text-value" 
							:style="{ color: appliedProgressConfig.color }"
						>
							{{ parseFloat(row.progress) || 0 }}%
						</span>
					</div>
					
					<div v-else class="progress-none">
						<span class="progress-none-value">
							{{ parseFloat(row.progress) || 0 }}%
						</span>
					</div>
				</template>

				<!-- 剩余工作量列 -->
                <template #remainingWork="{row}">
                    <span>{{ calculateRemainingWork(row) }}条</span>
                </template>

				<template #buttons="{row}">
					<el-button
						v-if="row.status === 1"
						type="primary"
						size="small"
						:disabled="
							currentUserInfoRole.baseRoles.includes('管理员') ||
							currentUserInfoRole.staffRole.includes('区县台账运维员')
						"
						@click="onTableButtonClick({btn: {code: 'link'}, row})"
					>
						关联业务表
					</el-button>

					<el-button
						v-if="row.status === 2"
						type="primary"
						size="small"
						:disabled="
							currentUserInfoRole.baseRoles.includes('管理员') ||
							currentUserInfoRole.staffRole.includes('区县台账运维员')
						"
						@click="onTableButtonClick({btn: {code: 'detail'}, row})"
					>
						查看
					</el-button>

					<el-dropdown v-if="row.status === 2" style="margin-left: 12px">
						<el-button
							size="small"
							:disabled="
								currentUserInfoRole.baseRoles.includes('管理员') ||
								currentUserInfoRole.staffRole.includes('区县台账运维员')
							"
						>
							更多
							<el-icon class="el-icon--right">
								<arrow-down />
							</el-icon>
						</el-button>

						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item>
									<span
										@click="
											currentUserInfoRole.baseRoles.includes('管理员') ||
											currentUserInfoRole.staffRole.includes('区县台账运维员')
												? ''
												: openConfig(row.id)
										"
										>更改配置</span
									>
								</el-dropdown-item>

								<!-- <el-dropdown-item>
									<span
										@click="
											currentUserInfoRole.baseRoles.includes('管理员') ||
											currentUserInfoRole.staffRole.includes('区县台账运维员')
												? ''
												: openCancel(row.id)
										"
										>取消关联</span
									>
								</el-dropdown-item> -->
								<el-dropdown-item>
									<span
										@click="
											remindFunc"
										>催办</span
									>
								</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</template>
			</TableV2>
			<Pagination
				:total="pagination.total"
				:current-page="pagination.page"
				:page-size="pagination.size"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			></Pagination>
		</Block>

		<!-- 子任务列表弹窗 -->
		<Dialog
			title="子任务"
			:enableButton="false"
			:width="'70%'"
			v-model="dialogSon"
			@heightChanged="heightChanged"
			@click-close="dialogSon = false"
		>
			<!-- 搜索表单 -->
			<div class="search">
				<Form
					v-model="searchFormSon"
					:data="formItemsSon"
					:enable-reset="false"
					label-width="0"
					column-count="4"
					button-vertical="flowing"
					confirm-text="查询"
					@submit="onSearchSon('search')"
					@clear="onSearchSon('clear')"
				>
				</Form>
			</div>

			<TableV2
				ref="tableRefSon"
				url="/api/ledger/telecom-task"
				:headers="{Urlkey: 'base'}"
				:req-params="reqParamsSon"
				:columns="tableColumnsSon"
				:enable-edit="false"
				:enable-delete="false"
				:enable-selection="false"
				:enable-latest-data="false"
				:height="tableHeightSon"
				:enable-toolbar="false"
				:form-label-width="100"
				:auto-height="true"
				@completed="onTableCompletedSon"
				@click-button="onTableButtonClickSon"
			>
				<template #taskName="{row}">
					<span
						class="link-click"
						@click="onTableButtonClickSon({btn: {code: 'detail'}, row})"
						>{{ row.taskName }}</span
					>
				</template>
				<!-- 责任人 -->
				<template #chargePersons="{row}">
					<div v-if="row.chargePersons.length">
						<!-- chargePersonUsername -->
						<!-- <span v-for="(item, idx) in row.chargePersons">{{ item.chargePersonUsername }}、</span> -->
						{{ row.chargePersons.map((item) => item.chargePersonName).toString() }}
					</div>
					<div v-else></div>
				</template>

				<template #date="{row}">
					<div v-if="row.startTime">
						<span>{{ row.startTime }}~{{ row.endTime }}</span>
					</div>
					<!-- <div v-else>--</div> -->
				</template>

				<template #status="{row}">
					<div flex="~" items-center>
						<span
							mr-5px
							inline-block
							w-10px
							h-10px
							rounded-full
							:style="{
								backgroundColor: FlowTaskStatus.filter(
									(v) => v.value === row.status
								)[0].color,
							}"
						></span>
						<span
							:style="{
								color: FlowTaskStatus.filter((v) => v.value === row.status)[0]
									.color,
							}"
						>
							{{ FlowTaskStatus.filter((v) => v.value === row.status)[0].label }}
						</span>
					</div>
				</template>

				<template #progress="{row}">
					<span>{{ row.progress }}%</span>
				</template>

				<template #buttons="{row}">
					<el-button
						v-if="row.status === 1"
						type="primary"
						size="small"
						@click="onTableButtonClickSon({btn: {code: 'link'}, row})"
					>
						关联业务表
					</el-button>

					<el-button
						v-if="row.status === 2"
						type="primary"
						size="small"
						@click="onTableButtonClickSon({btn: {code: 'detail'}, row})"
					>
						查看
					</el-button>

					<el-dropdown v-if="row.status === 2" style="margin-left: 12px">
						<el-button size="small">
							更多
							<el-icon class="el-icon--right">
								<arrow-down />
							</el-icon>
						</el-button>

						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item>
									<span @click="openConfigSon(row.id)">更改配置</span>
								</el-dropdown-item>

								<el-dropdown-item>
									<span @click="openCancelSon(row.id)">取消关联</span>
								</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</template>
			</TableV2>
			<Pagination
				:total="paginationSon.total"
				:current-page="paginationSon.page"
				:page-size="paginationSon.size"
				@current-change="onPaginationChangeSon($event, 'page')"
				@size-change="onPaginationChangeSon($event, 'size')"
			></Pagination>
		</Dialog>
		<Dialog
		v-model="urging"
		width="600px"
		title="催办"
		@close="urging = false"
		@click-close="urging = false"
		@click-confirm="confirmUrging"
	>
		<div class="urging">
			<p>请选择催办通知发送方式</p>
			<el-checkbox-group v-model="urgingList">
						<el-checkbox
					class="dropdown-child"
					v-for="child of (currentChild as any)"
					:key="child.label"
					:label="child.label"
					@change="onClickChild(child)"
				>
					{{ child.name }}
				</el-checkbox>
			</el-checkbox-group>
		</div>
	</Dialog>

	<!-- 进度动画设置弹窗 -->
	<Dialog
		v-model="showProgressAnimationDialog"
		width="1000px"
		title="进度动画设置"
		@close="closeProgressAnimationDialog"
		@click-close="closeProgressAnimationDialog"
	>
		<div class="progress-animation-settings">
			<!-- 第1部分：进度条动画设置 -->
			<div class="animation-section">
				<div class="section-title">1 进度条动画设置</div>
				
				<div class="section-content">
					<div class="setting-group">
						<div class="group-label">选择动画效果：</div>
						<div class="effects-grid">
							<div 
								v-for="effect in progressAnimationTypeOptions"
								:key="effect.type"
								class="effect-card"
								:class="{ selected: tempProgressConfig.type === effect.type }"
								@click="selectTempProgressType(effect.type)"
							>
								<!-- 进度演示区域 -->
								<div class="card-demo-area">
									<div v-if="effect.type === 'standard'" class="card-progress-demo"></div>
									<div v-else-if="effect.type === 'circle'" class="card-circle-demo"></div>
									<div v-else-if="effect.type === 'text'" class="card-text-demo">85%</div>
									<div v-else class="card-none-demo">85%</div>
								</div>
								<!-- 名称和描述 -->
								<div class="card-info">
									<div class="effect-name">{{ effect.name }}</div>
									<div class="effect-demo">{{ effect.description }}</div>
								</div>
							</div>
						</div>
					</div>
					
					<div class="setting-group">
						<div class="group-label">颜色设置：</div>
						<div class="color-options">
							<div class="color-group" v-for="(colorInfo, index) in presetColorsWithNames" :key="index">
								<div 
									class="color-block"
									:class="{ selected: tempProgressConfig.color === colorInfo.color }"
									:style="{ backgroundColor: colorInfo.color }"
									@click="selectTempColor('progress', colorInfo.color)"
								/>
								<div class="color-label-text">{{ colorInfo.name }}</div>
							</div>
							<div class="color-group">
								<el-color-picker 
									v-model="tempProgressConfig.color"
									class="custom-color-picker"
									size="small"
								/>
								<div class="color-label-text">自定义</div>
							</div>
						</div>
					</div>
					
					<div class="apply-button-container">
						<el-button type="primary" @click="applyAnimationConfig">应用配置</el-button>
					</div>
				</div>
			</div>
			
			<!-- 第2部分：进度追踪列表加载动画设置 -->
			<div class="animation-section">
				<div class="section-title">2 进度追踪列表加载动画设置</div>
				
				<div class="section-content">
					<div class="setting-group">
						<div class="group-label">选择动画效果：</div>
						<div class="effects-grid">
							<div 
								v-for="effect in loadingAnimationTypeOptions" 
								:key="effect.type"
								class="effect-card"
								:class="{ selected: tempLoadingConfig.type === effect.type }"
								@click="selectTempLoadingType(effect.type)"
							>
								<!-- 加载动画演示区域 -->
								<div class="card-demo-area">
									<div v-if="effect.type === 'spin'" class="card-spin-demo"></div>
									<div v-else-if="effect.type === 'pulse'" class="card-pulse-demo"></div>
									<div v-else-if="effect.type === 'bar'" class="card-bar-demo"></div>
									<div v-else-if="effect.type === 'fade'" class="card-fade-demo"></div>
									<div v-else class="card-skeleton-demo">
										<div class="skeleton-line"></div>
										<div class="skeleton-line short"></div>
									</div>
								</div>
								<!-- 名称和描述 -->
								<div class="card-info">
									<div class="effect-name">{{ effect.name }}</div>
									<div class="effect-demo">{{ effect.description }}</div>
								</div>
							</div>
						</div>
					</div>
					
					<div class="setting-group">
						<div class="group-label">颜色设置：</div>
						<div class="color-options">
							<div class="color-group" v-for="(colorInfo, index) in presetColorsWithNames" :key="index">
								<div 
									class="color-block"
									:class="{ selected: tempLoadingConfig.color === colorInfo.color }"
									:style="{ backgroundColor: colorInfo.color }"
									@click="selectTempColor('loading', colorInfo.color)"
								/>
								<div class="color-label-text">{{ colorInfo.name }}</div>
							</div>
							<div class="color-group">
								<el-color-picker 
									v-model="tempLoadingConfig.color"
									class="custom-color-picker"
									size="small"
								/>
								<div class="color-label-text">自定义</div>
							</div>
						</div>
					</div>
					
					<div class="apply-button-container">
						<el-button type="primary" @click="applyAnimationConfig">应用配置</el-button>
					</div>
				</div>
			</div>
		</div>
		

	</Dialog>
</div>
</template>

<style scoped>
/* 进度动画设置弹窗样式 */
.progress-animation-settings {
  padding: 24px;
  background: #ffffff;
  min-height: 600px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 动画设置区域 */
.animation-section {
  margin-bottom: 24px;
  background: #ffffff;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 24px;
  box-shadow: none;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #1a73e8;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 3px;
  height: 16px;
  background: #1a73e8;
  margin-right: 8px;
  border-radius: 2px;
}

.section-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 动画效果选择区域 */
.animation-effects {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.effects-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.effects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 12px;
}

/* 动画效果卡片 */
.effect-card {
  border: 1px solid #dadce0;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  background: #ffffff;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.effect-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: transparent;
  transition: all 0.2s ease;
}

.effect-card:hover {
  border-color: #1a73e8;
  box-shadow: 0 2px 10px rgba(26, 115, 232, 0.15);
  transform: translateY(-2px);
}

.effect-card:hover::before {
  background: #1a73e8;
}

.effect-card.selected {
  border-color: #1a73e8;
  background: #f8f9ff;
  box-shadow: 0 2px 10px rgba(26, 115, 232, 0.15);
}

.effect-card.selected::before {
  background: #1a73e8;
}

.effect-name {
  font-size: 13px;
  font-weight: 500;
  color: #202124;
  margin-top: 8px;
}

.effect-demo {
  font-size: 11px;
  color: #5f6368;
  margin-top: 4px;
  line-height: 1.3;
}

/* 卡片演示区域 */
.card-demo-area {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40px;
  margin-bottom: 12px;
}

/* 卡片中的进度演示 */
.card-progress-demo {
  width: 80px;
  height: 6px;
  background: #e8eaed;
  border-radius: 3px;
  position: relative;
  overflow: hidden;
}

.card-progress-demo::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 65%;
  height: 100%;
  background: #1a73e8;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.card-circle-demo {
  width: 36px;
  height: 36px;
  border: 3px solid #e8eaed;
  border-top-color: #1a73e8;
  border-radius: 50%;
  animation: spin 2s linear infinite;
}

.card-text-demo {
  font-size: 16px;
  font-weight: 600;
  color: #1a73e8;
  padding: 4px 12px;
  background: rgba(26, 115, 232, 0.1);
  border-radius: 4px;
}

.card-none-demo {
  font-size: 14px;
  color: #5f6368;
  padding: 4px 8px;
  border: 1px dashed #dadce0;
  border-radius: 4px;
}

/* 卡片信息区域 */
.card-info {
  text-align: center;
}

/* 加载动画演示样式 */
.card-spin-demo {
  width: 28px;
  height: 28px;
  border: 2px solid #e8eaed;
  border-top-color: #1a73e8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.card-pulse-demo {
  width: 32px;
  height: 32px;
  background: #1a73e8;
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

.card-bar-demo {
  width: 80px;
  height: 4px;
  background: #e8eaed;
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.card-bar-demo::after {
  content: '';
  position: absolute;
  left: -100%;
  top: 0;
  width: 50%;
  height: 100%;
  background: linear-gradient(90deg, transparent, #1a73e8, transparent);
  animation: loading-slide 2s infinite;
}

.card-fade-demo {
  width: 24px;
  height: 24px;
  background: #1a73e8;
  border-radius: 4px;
  animation: fade 1.5s ease-in-out infinite alternate;
}

.card-skeleton-demo {
  width: 80px;
}

.skeleton-line {
  height: 8px;
  background: #e8eaed;
  border-radius: 4px;
  margin-bottom: 4px;
  animation: skeleton-loading 1.5s ease-in-out infinite alternate;
}

.skeleton-line.short {
  width: 60%;
  margin-bottom: 0;
}

/* 动画关键帧 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(0.9);
  }
}

@keyframes loading-slide {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes fade {
  0% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}

@keyframes skeleton-loading {
  0% {
    background-color: #e8eaed;
  }
  100% {
    background-color: #f1f3f4;
  }
}

/* 颜色选择区域 */
.color-selection {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.color-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.color-options {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex-wrap: wrap;
}

.color-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.color-block {
  width: 48px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  border: 1px solid #dadce0;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.color-block:hover {
  transform: scale(1.05);
  border-color: #1a73e8;
  box-shadow: 0 2px 8px rgba(26, 115, 232, 0.2);
}

.color-block.selected {
  border-color: #1a73e8;
  border-width: 2px;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

.color-block.selected::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 12px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
}

.color-label-text {
  font-size: 10px;
  color: #5f6368;
  text-align: center;
  margin-top: 2px;
  line-height: 1;
}

/* 自定义颜色按钮 */
.custom-color-btn {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #f9ca24, #f0932b, #eb4d4b);
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
  border: 2px solid #e5e7eb;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.custom-color-btn::before {
  content: '+';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 16px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

.custom-color-btn:hover {
  transform: scale(1.1);
  border-color: #9ca3af;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 应用配置按钮 */
.apply-config-section {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #e1e5e9;
  margin-top: 20px;
}

.apply-config-btn {
  background: #1a73e8;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  min-width: 80px;
}

.apply-config-btn:hover {
  background: #1557b0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
}

.apply-config-btn:active {
  background: #1143a0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 自定义颜色选择器遮罩 */
.color-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.custom-color-picker {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* 进度显示样式 */
.progress-standard {
  width: 120px;
}

.progress-circle {
  display: flex;
  justify-content: center;
  align-items: center;
}

.progress-text {
  display: flex;
  justify-content: center;
  align-items: center;
}

.progress-text-value {
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(64, 158, 255, 0.1);
}

.progress-none {
  display: flex;
  justify-content: center;
  align-items: center;
}

.progress-none-text {
  color: #6b7280;
  font-size: 14px;
}

/* 动画演示效果 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

.demo-spin {
  animation: spin 2s linear infinite;
}

.demo-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.demo-fade {
  animation: fadeIn 2s ease-in-out infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .progress-animation-settings {
    padding: 12px;
  }
  
  .effects-grid {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 8px;
  }
  
  .effect-card {
    padding: 12px;
    min-height: 60px;
  }
  
  .color-options {
    gap: 8px;
  }
  
  .color-block {
    width: 28px;
    height: 28px;
  }
  
  .custom-color-btn {
    width: 28px;
    height: 28px;
  }
}
</style>

<route>
	{
		meta: {
			title: '进度追踪',
		},
	}
</route>
